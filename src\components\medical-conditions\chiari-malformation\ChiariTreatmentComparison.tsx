import { Sciss<PERSON>, Pill, Activity, CheckCircle, AlertTriangle, Clock, TrendingUp, Target } from 'lucide-react';
import React, { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface Treatment {
  name: string;
  description: string;
  indications: string[];
  advantages: string[];
  disadvantages: string[];
  successRate: string;
  duration: string;
  complications: string[];
}

interface ChiariTreatmentComparisonProps {
  title: string;
  description: string;
  treatments: Treatment[];
}

export function ChiariTreatmentComparison({ 
  title, 
  description, 
  treatments 
}: ChiariTreatmentComparisonProps) {
  const deviceInfo = useDeviceDetection();
  const [activeTab, setActiveTab] = useState('overview');

  const getSuccessRateValue = (successRate: string) => {
    const match = successRate.match(/(\d+)-?(\d+)?%/);
    if (match) {
      return match[2] ? parseInt(match[2]) : parseInt(match[1]);
    }
    return 50;
  };

  const getTreatmentIcon = (name: string) => {
    if (name.includes('Surgical')) return Scissors;
    if (name.includes('Conservative')) return Activity;
    if (name.includes('Symptom')) return Pill;
    return Target;
  };

  const getTreatmentColor = (name: string) => {
    if (name.includes('Surgical')) return 'text-foreground bg-info-light border-info/30';
    if (name.includes('Conservative')) return 'text-foreground bg-success-light border-success/30';
    if (name.includes('Symptom')) return 'text-medical-blue bg-medical-blue-light border-medical-blue/30';
    return 'text-muted-foreground bg-muted border-border';
  };

  const getApproachType = (name: string) => {
    if (name.includes('Surgical')) return { label: 'Surgical', colour: 'bg-info-light/30 text-foreground' };
    if (name.includes('Conservative')) return { label: 'Conservative', colour: 'bg-success-light/30 text-foreground' };
    if (name.includes('Symptom')) return { label: 'Medical', colour: 'bg-medical-blue text-medical-blue' };
    return { label: 'Other', colour: 'bg-muted text-muted-foreground' };
  };

  return (
    <section className={cn("py-16 bg-muted", deviceInfo.isMobile ? "px-4" : "")}>
      <div className="container">
        <div className="text-centre mb-12">
          <h2 className={cn(
            "font-bold mb-4",
            deviceInfo.isMobile ? "text-2xl" : "text-3xl"
          )}>
            {title}
          </h2>
          <p className={cn(
            "text-muted-foreground max-w-3xl mx-auto",
            deviceInfo.isMobile ? "text-sm" : "text-lg"
          )}>
            {description}
          </p>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className={cn(
            "grid w-full mb-8",
            deviceInfo.isMobile ? "grid-cols-1 h-auto" : "grid-cols-2"
          )}>
            <TabsTrigger 
              value="overview"
              className={cn(
                "text-centre",
                deviceInfo.isMobile ? "py-3 text-sm" : "py-4"
              )}
            >
              Treatment Overview
            </TabsTrigger>
            <TabsTrigger 
              value="comparison"
              className={cn(
                "text-centre",
                deviceInfo.isMobile ? "py-3 text-sm" : "py-4"
              )}
            >
              Detailed Comparison
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-8">
            {/* Treatment Success Rates */}
            <Card className="medical-card">
              <CardHeader>
                <CardTitle className="flex items-centre gap-2">
                  <TrendingUp className="h-5 w-5 text-primary" />
                  Treatment Success Rates
                </CardTitle>
                <CardDescription>
                  Comparison of effectiveness across different treatment approaches
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {treatments.map((treatment, index) => {
                    const successValue = getSuccessRateValue(treatment.successRate);
                    const approachType = getApproachType(treatment.name);
                    
                    return (
                      <div key={index} className="space-y-2">
                        <div className="flex items-centre justify-between">
                          <div className="flex items-centre gap-2">
                            <span className="font-medium">{treatment.name}</span>
                            <Badge className={approachType.colour}>
                              {approachType.label}
                            </Badge>
                          </div>
                          <span className="text-sm font-medium">{treatment.successRate}</span>
                        </div>
                        <Progress value={successValue} className="h-2" />
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>

            {/* Quick Comparison Grid */}
            <div className="grid gap-6 md:grid-cols-3">
              {treatments.map((treatment, index) => {
                const Icon = getTreatmentIcon(treatment.name);
                const approachType = getApproachType(treatment.name);
                
                return (
                  <Card key={index} className={cn("transition-all duration-200", getTreatmentColor(treatment.name))}>
                    <CardHeader>
                      <div className="flex items-centre justify-between">
                        <div className="flex items-centre gap-2">
                          <div className="p-2 rounded-full medical-card/80 border border-border/50">
                            <Icon className="h-5 w-5" />
                          </div>
                          <div>
                            <CardTitle className="text-lg">{treatment.name}</CardTitle>
                          </div>
                        </div>
                        <Badge className={approachType.colour}>
                          {approachType.label}
                        </Badge>
                      </div>
                      <CardDescription className="text-sm">{treatment.description}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="grid gap-3 text-sm">
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Success Rate:</span>
                            <span className="font-medium">{treatment.successRate}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Duration:</span>
                            <span className="font-medium">{treatment.duration}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Complications:</span>
                            <span className="font-medium">{treatment.complications.length} types</span>
                          </div>
                        </div>
                        
                        <div className="space-y-2">
                          <h4 className="font-medium text-sm">Key Advantages:</h4>
                          <ul className="space-y-1">
                            {treatment.advantages.slice(0, 2).map((advantage, idx) => (
                              <li key={idx} className="text-xs flex items-start gap-2">
                                <CheckCircle className="h-3 w-3 text-success mt-0.5 flex-shrink-0" />
                                {advantage}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </TabsContent>

          <TabsContent value="comparison" className="space-y-6">
            {treatments.map((treatment, index) => {
              const Icon = getTreatmentIcon(treatment.name);
              const approachType = getApproachType(treatment.name);
              
              return (
                <Card key={index} className="medical-card">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="p-2 rounded-full bg-primary/10">
                          <Icon className="h-5 w-5 text-primary" />
                        </div>
                        <div>
                          <CardTitle className="text-xl">{treatment.name}</CardTitle>
                          <CardDescription>{treatment.description}</CardDescription>
                        </div>
                      </div>
                      <Badge className={approachType.colour}>
                        {approachType.label}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {/* Key Metrics */}
                    <div className={cn(
                      "grid gap-6",
                      deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-3"
                    )}>
                      <div>
                        <h4 className="text-enhanced-heading font-semibold mb-2 flex items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-success" />
                          Success Rate
                        </h4>
                        <p className="text-sm text-muted-foreground">{treatment.successRate}</p>
                      </div>
                      
                      <div>
                        <h4 className="font-semibold mb-2 flex items-centre gap-2">
                          <Clock className="h-4 w-4 text-info" />
                          Duration
                        </h4>
                        <p className="text-sm text-muted-foreground">{treatment.duration}</p>
                      </div>

                      <div>
                        <h4 className="font-semibold mb-2 flex items-centre gap-2">
                          <AlertTriangle className="h-4 w-4 text-info" />
                          Risk Level
                        </h4>
                        <p className="text-sm text-muted-foreground">
                          {treatment.complications.length} potential complications
                        </p>
                      </div>
                    </div>

                    {/* Detailed Information */}
                    <div className={cn(
                      "grid gap-6",
                      deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2"
                    )}>
                      <div>
                        <h4 className="font-semibold mb-3 text-info">Indications</h4>
                        <ul className="space-y-2">
                          {treatment.indications.map((indication, idx) => (
                            <li key={idx} className="flex items-start gap-2">
                              <div className="w-2 h-2 bg-info rounded-full mt-2 flex-shrink-0" />
                              <span className="text-sm">{indication}</span>
                            </li>
                          ))}
                        </ul>
                      </div>

                      <div>
                        <h4 className="font-semibold mb-3 text-success">Advantages</h4>
                        <ul className="space-y-2">
                          {treatment.advantages.map((advantage, idx) => (
                            <li key={idx} className="flex items-start gap-2">
                              <CheckCircle className="h-4 w-4 text-success mt-0.5 flex-shrink-0" />
                              <span className="text-sm">{advantage}</span>
                            </li>
                          ))}
                        </ul>
                      </div>

                      <div>
                        <h4 className="font-semibold mb-3 text-foreground">Disadvantages</h4>
                        <ul className="space-y-2">
                          {treatment.disadvantages.map((disadvantage, idx) => (
                            <li key={idx} className="flex items-start gap-2">
                              <AlertTriangle className="h-4 w-4 text-foreground mt-0.5 flex-shrink-0" />
                              <span className="text-sm">{disadvantage}</span>
                            </li>
                          ))}
                        </ul>
                      </div>

                      <div>
                        <h4 className="font-semibold mb-3 text-info">Potential Complications</h4>
                        <ul className="space-y-2">
                          {treatment.complications.map((complication, idx) => (
                            <li key={idx} className="flex items-start gap-2">
                              <div className="w-2 h-2 bg-info rounded-full mt-2 flex-shrink-0" />
                              <span className="text-sm">{complication}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>

                    {/* Clinical Notes */}
                    <div className="bg-info-light border border-info/30 rounded-lg p-4">
                      <h4 className="font-semibold text-foreground mb-2">Clinical Considerations</h4>
                      <p className="text-sm text-foreground">
                        {treatment.name.includes('Surgical') && 
                          "Surgical decompression is the definitive treatment for symptomatic Chiari malformation. The goal is to relieve compression and restore normal cerebrospinal fluid flow. Success rates are high when patients are carefully selected and surgery is performed by experienced neurosurgeons."
                        }
                        {treatment.name.includes('Conservative') && 
                          "Conservative management is appropriate for asymptomatic patients or those with mild symptoms. Regular monitoring is essential to detect progression. This approach avoids surgical risks but does not address the underlying anatomical problem."
                        }
                        {treatment.name.includes('Symptom') && 
                          "Symptom management can provide significant quality of life improvement while monitoring for progression. This approach is often used in conjunction with other treatments and helps patients maintain function while making treatment decisions."
                        }
                      </p>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </TabsContent>
        </Tabs>

        {/* Treatment Decision Guide */}
        <div className="mt-12">
          <Card className="medical-card bg-info-light border border-info/30">
            <CardHeader>
              <CardTitle className="text-enhanced-heading flex items-centre gap-2">
                <Target className="h-5 w-5 text-primary" />
                Treatment Decision Guide
              </CardTitle>
              <CardDescription className="text-enhanced-body">
                Factors to consider when choosing the most appropriate treatment approach
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className={cn(
                "grid gap-6",
                deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-3"
              )}>
                <div className="text-centre p-4 medical-card-inner rounded-lg border border-enhanced-border">
                  <div className="w-12 h-12 bg-primary text-primary-foreground rounded-full flex items-centre justify-centre font-bold mx-auto mb-3">
                    1
                  </div>
                  <h4 className="text-enhanced-heading font-semibold mb-2">Symptom Severity</h4>
                  <p className="text-sm text-muted-foreground">
                    Assess impact on daily life, work, and quality of life
                  </p>
                </div>
                <div className="text-centre p-4 bg-background rounded-lg border">
                  <div className="w-12 h-12 bg-primary text-primary-foreground rounded-full flex items-centre justify-centre font-bold mx-auto mb-3">
                    2
                  </div>
                  <h4 className="font-semibold mb-2">Progression Risk</h4>
                  <p className="text-sm text-muted-foreground">
                    Consider likelihood of symptom worsening over time
                  </p>
                </div>
                <div className="text-centre p-4 bg-background rounded-lg border">
                  <div className="w-12 h-12 bg-primary text-primary-foreground rounded-full flex items-centre justify-centre font-bold mx-auto mb-3">
                    3
                  </div>
                  <h4 className="font-semibold mb-2">Individual Factors</h4>
                  <p className="text-sm text-muted-foreground">
                    Age, health status, personal preferences, and surgical risk
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Call to Action */}
        <div className="text-centre mt-12">
          <Card className="bg-info-light border border-info/30">
            <CardContent className="pt-6">
              <h3 className="font-semibold mb-2">Expert Chiari Malformation Treatment</h3>
              <p className="text-muted-foreground mb-4">
                Our neurosurgical team provides comprehensive evaluation and treatment for Chiari malformation. 
                Personalised treatment plans based on individual symptoms, anatomy, and preferences.
              </p>
              <Button size={deviceInfo.isMobile ? "default" : "lg"}>
                Schedule Consultation
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
}

export default ChiariTreatmentComparison;
