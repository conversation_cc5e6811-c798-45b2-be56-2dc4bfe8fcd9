import React, { useEffect } from 'react';
import { Helmet } from 'react-helmet-async';

import {
  FacialAnatomySection,
  SpasmProgressionSection,
  HemifacialTreatmentComparison,
  HemifacialSymptomAssessment
} from '@/components/medical-conditions/hemifacial-spasm';
import {
  ConditionHero,
  ConditionOverviewSection,
  ConditionQuickFacts
} from '@/components/medical-conditions/shared';
import StandardPageLayout from '@/components/StandardPageLayout';
import { hemifacialSpasmData } from '@/data/conditions/hemifacialSpasm';
import { useScrollToTop } from '@/hooks/useScrollToTop';

const HemifacialSpasm: React.FC = () => {
  useScrollToTop();

  useEffect(() => {
    // Track page view for analytics
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('config', 'GA_MEASUREMENT_ID', {
        page_title: 'Hemifacial Spasm Guide',
        page_location: window.location.href,
      });
    }
  }, []);

  return (
    <>
      <Helmet>
        <title>Hemifacial Spasm: Comprehensive Patient Guide | miNEURO</title>
        <meta 
          name="description" 
          content="Complete guide to hemifacial spasm: causes, symptoms, diagnosis, and treatment options. Expert neurosurgical care with botulinum toxin injections and advanced surgical techniques." 
        />
        <meta 
          name="keywords" 
          content="hemifacial spasm, facial spasm, facial nerve, botulinum toxin, botox, microvascular decompression, facial twitching, neurosurgery, Melbourne neurosurgeon" 
        />
        <meta name="author" content="Dr. Ales Aliashkevich" />
        <meta property="og:title" content="Hemifacial Spasm: Comprehensive Patient Guide | miNEURO" />
        <meta 
          property="og:description" 
          content="Expert guide to hemifacial spasm covering causes, symptoms, diagnosis, and advanced treatment options including botulinum toxin and surgical approaches." 
        />
        <meta property="og:type" content="article" />
        <meta property="og:url" content="https://mineuro.com.au/patient-resources/conditions/hemifacial-spasm" />
        <meta property="og:image" content="https://mineuro.com.au/images/neurological-conditions/hemifacial-spasm-guide-og.jpg" />
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="Hemifacial Spasm: Comprehensive Patient Guide" />
        <meta name="twitter:description" content="Complete guide to hemifacial spasm with expert neurosurgical insights and treatment options." />
        <link rel="canonical" href="https://mineuro.com.au/patient-resources/conditions/hemifacial-spasm" />
        
        {/* Structured Data for Medical Content */}
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "MedicalWebPage",
            "name": "Hemifacial Spasm: Comprehensive Patient Guide",
            "description": "Complete guide to hemifacial spasm: causes, symptoms, diagnosis, and treatment options",
            "url": "https://mineuro.com.au/patient-resources/conditions/hemifacial-spasm",
            "mainEntity": {
              "@type": "MedicalCondition",
              "name": "Hemifacial Spasm",
              "alternateName": ["Facial Spasm", "Unilateral Facial Spasm"],
              "description": "Neurological disorder characterised by involuntary, intermittent contractions of muscles on one side of the face",
              "symptom": [
                "Eyelid twitching",
                "Facial muscle spasms",
                "Unilateral facial contractions",
                "Progressive facial involvement",
                "Stress-triggered spasms"
              ],
              "riskFactor": [
                "Vascular compression of facial nerve",
                "Female gender",
                "Middle age",
                "Hypertension"
              ]
            },
            "author": {
              "@type": "Person",
              "name": "Dr. Ales Aliashkevich",
              "jobTitle": "Neurosurgeon",
              "affiliation": {
                "@type": "Organization",
                "name": "miNEURO Brain and Spine Surgery"
              }
            },
            "datePublished": "2024-01-01",
            "dateModified": new Date().toISOString().split('T')[0],
            "publisher": {
              "@type": "Organization",
              "name": "miNEURO Brain and Spine Surgery",
              "url": "https://mineuro.com.au"
            }
          })}
        </script>
      </Helmet>

      <StandardPageLayout 
        title="Hemifacial Spasm - Comprehensive Guide" 
        showHeader={false}
      >
        <main className="flex-1 pt-20">
          {/* Hero Section */}
          <ConditionHero
            title={hemifacialSpasmData.hero.title}
            subtitle={hemifacialSpasmData.hero.subtitle}
            backgroundImage={hemifacialSpasmData.hero.backgroundImage}
            badge={hemifacialSpasmData.hero.badge}
            showAssessment={true}
            showBooking={true}
            assessmentLink="#symptom-assessment"
            bookingLink="/appointments"
          />

          {/* Quick Facts */}
          <ConditionQuickFacts facts={hemifacialSpasmData.quickFacts} />

          {/* Overview Section */}
          <ConditionOverviewSection
            title={hemifacialSpasmData.overview.title}
            description={hemifacialSpasmData.overview.description}
            keyPoints={hemifacialSpasmData.overview.keyPoints}
            imageSrc={hemifacialSpasmData.overview.imageSrc}
            imageAlt={hemifacialSpasmData.overview.imageAlt}
            imageCaption={hemifacialSpasmData.overview.imageCaption}
          />

          {/* Symptom Assessment Tool */}
          <div id="symptom-assessment">
            <HemifacialSymptomAssessment />
          </div>

          {/* Facial Nerve Anatomy */}
          <FacialAnatomySection
            title={hemifacialSpasmData.anatomy.title}
            description={hemifacialSpasmData.anatomy.description}
            facialNerveComponents={hemifacialSpasmData.anatomy.facialNerveComponents}
          />

          {/* Types and Classifications */}
          <section className="py-20 bg-background">
            <div className="container">
              <div className="text-center mb-16">
                <h2 className="text-enhanced-heading text-3xl lg:text-enhanced-heading text-4xl font-bold text-foreground mb-6 leading-tight">
                  {hemifacialSpasmData.types.title}
                </h2>
                <p className="text-lg text-muted-foreground max-w-4xl mx-auto leading-relaxed">
                  {hemifacialSpasmData.types.description}
                </p>
              </div>

              <div className="grid gap-8 md:grid-cols-3">
                {hemifacialSpasmData.types.classifications.map((type, index) => (
                  <div
                    key={index}
                    className="medical-card/80 backdrop-blur-sm border border-border/50 rounded-xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 hover:border-primary/30"
                  >
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="font-bold text-xl text-foreground leading-tight">{type.type}</h3>
                      <span className={`text-xs px-3 py-1 rounded-full font-medium ${
                        type.type.includes('Primary') ? 'bg-primary/10 text-primary border border-primary/20' :
                        type.type.includes('Secondary') ? 'badge-medical border border-medical-blue' :
                        'badge-info border border-info'
                      }`}>
                        {type.prevalence.split(' ')[0]}
                      </span>
                    </div>
                    <p className="text-muted-foreground mb-6 leading-relaxed">{type.description}</p>

                    <div className="content-spacing-sm">
                      <div>
                        <h4 className="font-semibold text-sm text-foreground mb-3">Characteristics:</h4>
                        <ul className="space-y-2">
                          {type.characteristics.slice(0, 3).map((char, idx) => (
                            <li key={idx} className="text-sm flex items-start gap-3">
                              <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0" />
                              <span className="text-foreground font-medium leading-relaxed">{char}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                      
                      <div className="space-y-2 pt-2 border-t border-border">
                        <div className="flex justify-between items-centre">
                          <span className="text-enhanced-muted text-xs">Prognosis:</span>
                          <span className="text-xs font-medium">{type.prognosis}</span>
                        </div>
                        <div className="flex justify-between items-centre">
                          <span className="text-enhanced-muted text-xs">Treatment:</span>
                          <span className="text-xs font-medium">{type.treatmentResponse.split(' ').slice(0, 2).join(' ')}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Disease Progression */}
          <SpasmProgressionSection
            title={hemifacialSpasmData.progression.title}
            description={hemifacialSpasmData.progression.description}
            stages={hemifacialSpasmData.progression.stages}
          />

          {/* Causes and Risk Factors */}
          <section className="py-16 bg-gradient-to-br from-background via-background/95 to-muted/30">
            <div className="container">
              <div className="text-centre mb-12">
                <h2 className="text-enhanced-heading text-3xl font-bold mb-4 text-foreground">{hemifacialSpasmData.causes.title}</h2>
                <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
                  {hemifacialSpasmData.causes.description}
                </p>
              </div>

              <div className="content-spacing">
                {hemifacialSpasmData.causes.etiologies.map((etiology, index) => (
                  <div key={index} className="medical-card/90 backdrop-blur-sm border border-border/50 rounded-xl p-8 shadow-xl hover:shadow-xl transition-all duration-300">
                    <h3 className="font-bold text-xl mb-6 text-foreground">{etiology.category}</h3>
                    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                      {etiology.causes.map((cause, idx) => (
                        <div key={idx} className="border-l-4 border-primary/30 pl-6 bg-background/60 dark:bg-muted/60 backdrop-blur-sm rounded-lg p-4">
                          <div className="flex items-centre justify-between mb-3">
                            <h4 className="font-semibold text-foreground">{cause.cause}</h4>
                            <span className="text-xs px-3 py-1 rounded-full bg-info-light/80 text-foreground dark:bg-info/50 dark:text-foreground border border-info dark:border-info font-medium">
                              {cause.frequency}
                            </span>
                          </div>
                          <p className="text-sm text-foreground/90 mb-3 leading-relaxed">{cause.description}</p>
                          <p className="text-xs text-muted-foreground leading-relaxed">
                            <strong className="text-foreground">Mechanism:</strong> {cause.mechanism}
                          </p>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Symptoms Section */}
          <section className="py-16 bg-gradient-to-br from-muted/20 via-muted/30 to-muted/20 border-y border-border/50">
            <div className="container">
              <div className="text-centre mb-12">
                <h2 className="text-enhanced-heading text-3xl font-bold mb-4 text-foreground">Symptoms and Clinical Presentation</h2>
                <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
                  Hemifacial spasm symptoms typically begin around the eye and progressively involve the entire side of the face.
                </p>
              </div>

              <div className="grid gap-8 md:grid-cols-3">
                {hemifacialSpasmData.symptoms.map((category, index) => (
                  <div key={index} className="medical-card/90 backdrop-blur-sm border border-border/50 rounded-xl p-8 shadow-xl hover:shadow-xl transition-all duration-300 hover:scale-105">
                    <h3 className="flex items-centre gap-3 font-bold text-xl mb-6 text-foreground">
                      <div className="p-2 bg-primary/10 rounded-lg">
                        <category.icon className="h-5 w-5 text-primary" />
                      </div>
                      {category.category}
                    </h3>
                    <div className="content-spacing">
                      {category.symptoms.map((symptom, idx) => (
                        <div key={idx} className="border-l-4 border-primary/30 pl-6 bg-background/60 dark:bg-muted/60 backdrop-blur-sm rounded-lg p-4">
                          <div className="flex items-centre justify-between mb-2">
                            <h4 className="font-semibold text-foreground">{symptom.name}</h4>
                            <span className={`text-xs px-3 py-1 rounded-full font-medium border ${
                              symptom.severity === 'severe' ? 'bg-muted/80 text-foreground dark:bg-muted/50 dark:text-foreground border-border dark:border-border' :
                              symptom.severity === 'moderate' ? 'bg-info/80 text-foreground dark:bg-info/50 dark:text-info border-info dark:border-info' :
                              'bg-success/80 text-foreground dark:bg-success/50 dark:text-success border-success dark:border-success'
                            }`}>
                              {symptom.severity}
                            </span>
                          </div>
                          <p className="text-sm text-foreground/90 mb-2 leading-relaxed">{symptom.description}</p>
                          <p className="text-xs text-muted-foreground mb-1"><strong className="text-foreground">Frequency:</strong> {symptom.frequency}</p>
                          <p className="text-enhanced-muted text-xs"><strong className="text-foreground">Progression:</strong> {symptom.progression}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Diagnosis Section */}
          <section className="py-16 bg-gradient-to-br from-background via-background/95 to-muted/30">
            <div className="container">
              <div className="text-centre mb-12">
                <h2 className="text-enhanced-heading text-3xl font-bold mb-4 text-foreground">{hemifacialSpasmData.diagnosis.title}</h2>
                <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
                  {hemifacialSpasmData.diagnosis.description}
                </p>
              </div>

              {/* Clinical Criteria */}
              <div className="mb-12">
                <h3 className="text-enhanced-heading text-2xl font-bold mb-6 text-centre text-foreground">Clinical Diagnostic Criteria</h3>
                <div className="grid gap-6 md:grid-cols-2">
                  {hemifacialSpasmData.diagnosis.clinicalCriteria.map((criterion, index) => (
                    <div key={index} className="medical-card/90 backdrop-blur-sm border border-border/50 rounded-xl p-6 shadow-xl hover:shadow-xl transition-all duration-300">
                      <div className="flex items-centre justify-between mb-3">
                        <h4 className="font-semibold text-lg text-foreground">{criterion.criterion}</h4>
                        <span className={`text-xs px-3 py-1 rounded-full font-medium border ${
                          criterion.importance === 'essential' ? 'bg-muted/80 text-foreground dark:bg-muted/50 dark:text-foreground border-border dark:border-border' : 'bg-info/80 text-foreground dark:bg-info/50 dark:text-info border-info dark:border-info'
                        }`}>
                          {criterion.importance}
                        </span>
                      </div>
                      <p className="text-sm text-foreground/90 leading-relaxed">{criterion.description}</p>
                    </div>
                  ))}
                </div>
              </div>

              {/* Investigations */}
              <div className="mb-12">
                <h3 className="text-enhanced-heading text-2xl font-bold mb-6 text-centre text-foreground">Diagnostic Investigations</h3>
                <div className="grid gap-8 lg:grid-cols-3">
                  {hemifacialSpasmData.diagnosis.investigations.map((investigation, index) => (
                    <div key={index} className="medical-card/90 backdrop-blur-sm border border-border/50 rounded-xl p-6 shadow-xl hover:shadow-xl transition-all duration-300 hover:scale-105">
                      <h4 className="font-bold text-xl mb-3 text-foreground">{investigation.test}</h4>
                      <p className="text-foreground/90 mb-4 leading-relaxed">{investigation.description}</p>

                      <div className="content-spacing-sm">
                        <div className="bg-success/80 dark:bg-success/30 border border-success/70 dark:border-success/50 rounded-lg p-4">
                          <h5 className="font-semibold text-sm text-success dark:text-success mb-2">Accuracy</h5>
                          <p className="text-sm text-success dark:text-success leading-relaxed">{investigation.accuracy}</p>
                        </div>

                        <div className="bg-background/60 dark:bg-muted/60 backdrop-blur-sm rounded-lg p-4">
                          <h5 className="font-semibold text-sm mb-3 text-foreground">Indications:</h5>
                          <ul className="space-y-2">
                            {investigation.indications.slice(0, 3).map((indication, idx) => (
                              <li key={idx} className="text-sm flex items-start gap-3 text-foreground/90">
                                <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0" />
                                <span className="leading-relaxed">{indication}</span>
                              </li>
                            ))}
                          </ul>
                        </div>

                        <div className="bg-background/60 dark:bg-muted/60 backdrop-blur-sm rounded-lg p-4">
                          <h5 className="font-semibold text-sm mb-3 text-foreground">Findings:</h5>
                          <ul className="space-y-2">
                            {investigation.findings.slice(0, 2).map((finding, idx) => (
                              <li key={idx} className="text-sm flex items-start gap-3 text-foreground/90">
                                <div className="w-2 h-2 bg-info rounded-full mt-2 flex-shrink-0" />
                                <span className="leading-relaxed">{finding}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Differential Diagnosis */}
              <div>
                <h3 className="text-enhanced-heading text-2xl font-bold mb-6 text-centre text-foreground">Differential Diagnosis</h3>
                <div className="grid gap-6 md:grid-cols-3">
                  {hemifacialSpasmData.diagnosis.differentialDiagnosis.map((condition, index) => (
                    <div key={index} className="medical-card/90 backdrop-blur-sm border border-border/50 rounded-xl p-6 shadow-xl hover:shadow-xl transition-all duration-300 hover:scale-105">
                      <h4 className="font-bold text-lg mb-4 text-foreground">{condition.condition}</h4>
                      <div className="content-spacing-sm">
                        <div className="bg-background/60 dark:bg-muted/60 backdrop-blur-sm rounded-lg p-4">
                          <h5 className="font-semibold text-sm mb-3 text-foreground">Distinguishing Features:</h5>
                          <ul className="space-y-2">
                            {condition.distinguishingFeatures.map((feature, idx) => (
                              <li key={idx} className="text-sm flex items-start gap-3 text-foreground/90">
                                <div className="w-2 h-2 bg-medical-blue rounded-full mt-2 flex-shrink-0" />
                                <span className="leading-relaxed">{feature}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                        <div className="bg-medical-blue/80 dark:bg-medical-blue/30 border border-medical-blue/70 dark:border-medical-blue/50 rounded-lg p-4">
                          <h5 className="font-semibold text-sm text-medical-blue dark:text-medical-blue mb-2">Key Difference</h5>
                          <p className="text-sm text-medical-blue dark:text-medical-blue leading-relaxed">{condition.keyDifferences}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </section>

          {/* Treatment Comparison */}
          <HemifacialTreatmentComparison
            title={hemifacialSpasmData.treatmentModalities.title}
            description={hemifacialSpasmData.treatmentModalities.description}
            treatments={hemifacialSpasmData.treatmentModalities.treatments}
          />

          {/* Botulinum Toxin Treatment */}
          <section className="py-16 bg-gradient-to-br from-background via-background/95 to-muted/30">
            <div className="container">
              <div className="text-centre mb-12">
                <h2 className="text-enhanced-heading text-3xl font-bold mb-4 text-foreground">{hemifacialSpasmData.botoxTreatment.title}</h2>
                <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
                  {hemifacialSpasmData.botoxTreatment.description}
                </p>
              </div>

              {/* Mechanism */}
              <div className="mb-12">
                <div className="medical-card/90 backdrop-blur-sm border border-border/50 rounded-xl p-8 shadow-xl max-w-4xl mx-auto">
                  <h3 className="font-bold text-xl mb-4 text-foreground">Mechanism of Action</h3>
                  <p className="text-foreground/90 leading-relaxed">{hemifacialSpasmData.botoxTreatment.mechanism}</p>
                </div>
              </div>

              {/* Procedure Steps */}
              <div className="mb-12">
                <h3 className="text-enhanced-heading text-2xl font-bold mb-6 text-centre text-foreground">Treatment Procedure</h3>
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
                  {hemifacialSpasmData.botoxTreatment.procedure.map((step, index) => (
                    <div key={index} className="medical-card/90 backdrop-blur-sm border border-border/50 rounded-xl p-6 shadow-xl hover:shadow-xl transition-all duration-300 hover:scale-105">
                      <div className="w-12 h-12 bg-muted text-primary-foreground rounded-xl flex items-centre justify-centre font-bold mx-auto mb-4 shadow-lg">
                        {index + 1}
                      </div>
                      <h4 className="font-bold text-lg mb-3 text-centre text-foreground">{step.step}</h4>
                      <p className="text-sm text-foreground/90 mb-4 leading-relaxed">{step.description}</p>
                      <div className="bg-success/80 dark:bg-success/30 border border-success/70 dark:border-success/50 rounded-lg p-3">
                        <p className="text-xs text-success dark:text-success font-medium leading-relaxed">{step.technique}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Outcomes */}
              <div>
                <h3 className="text-enhanced-heading text-2xl font-bold mb-6 text-centre text-foreground">Treatment Outcomes</h3>
                <div className="grid gap-6 md:grid-cols-2 max-w-4xl mx-auto">
                  <div className="medical-card/90 backdrop-blur-sm border border-border/50 rounded-xl p-6 shadow-xl hover:shadow-xl transition-all duration-300">
                    <h4 className="font-bold text-lg mb-4 text-foreground">Effectiveness & Duration</h4>
                    <div className="content-spacing-sm">
                      <div className="bg-success/80 dark:bg-success/30 border border-success/70 dark:border-success/50 rounded-lg p-4">
                        <p className="text-sm text-success dark:text-success font-medium leading-relaxed">{hemifacialSpasmData.botoxTreatment.outcomes.effectiveness}</p>
                      </div>
                      <div className="bg-info/80 dark:bg-info/30 border border-info/70 dark:border-info/50 rounded-lg p-4">
                        <p className="text-sm text-info dark:text-info font-medium leading-relaxed">{hemifacialSpasmData.botoxTreatment.outcomes.duration}</p>
                      </div>
                    </div>
                  </div>

                  <div className="medical-card/90 backdrop-blur-sm border border-border/50 rounded-xl p-6 shadow-xl hover:shadow-xl transition-all duration-300">
                    <h4 className="font-bold text-lg mb-4 text-foreground">Side Effects & Contraindications</h4>
                    <div className="content-spacing-sm">
                      <div className="bg-background/60 dark:bg-muted/60 backdrop-blur-sm rounded-lg p-4">
                        <h5 className="font-semibold text-sm mb-3 text-foreground">Common Side Effects:</h5>
                        <ul className="space-y-2">
                          {hemifacialSpasmData.botoxTreatment.outcomes.sideEffects.slice(0, 3).map((effect, idx) => (
                            <li key={idx} className="text-sm flex items-start gap-3 text-foreground/90">
                              <div className="w-2 h-2 bg-info rounded-full mt-2 flex-shrink-0" />
                              <span className="leading-relaxed">{effect}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                      <div className="bg-background/60 dark:bg-muted/60 backdrop-blur-sm rounded-lg p-4">
                        <h5 className="font-semibold text-sm mb-3 text-foreground">Contraindications:</h5>
                        <ul className="space-y-2">
                          {hemifacialSpasmData.botoxTreatment.outcomes.contraindications.slice(0, 2).map((contra, idx) => (
                            <li key={idx} className="text-sm flex items-start gap-3 text-foreground/90">
                              <div className="w-2 h-2 bg-muted rounded-full mt-2 flex-shrink-0" />
                              <span className="leading-relaxed">{contra}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Surgical Options */}
          <section className="py-16 bg-gradient-to-br from-muted/20 via-muted/30 to-muted/20 border-y border-border/50">
            <div className="container">
              <div className="text-centre mb-12">
                <h2 className="text-enhanced-heading text-3xl font-bold mb-4 text-foreground">{hemifacialSpasmData.surgicalOptions.title}</h2>
                <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
                  {hemifacialSpasmData.surgicalOptions.description}
                </p>
              </div>

              <div className="grid gap-8 lg:grid-cols-2">
                {hemifacialSpasmData.surgicalOptions.procedures.map((procedure, index) => (
                  <div key={index} className="medical-card/90 backdrop-blur-sm border border-border/50 rounded-xl p-8 shadow-xl hover:shadow-xl transition-all duration-300 hover:scale-105">
                    <h3 className="font-bold text-xl mb-4 text-foreground">{procedure.name}</h3>
                    <p className="text-foreground/90 mb-6 leading-relaxed">{procedure.description}</p>

                    <div className="content-spacing">
                      <div className="bg-info/80 dark:bg-info/30 border border-info/70 dark:border-info/50 rounded-lg p-4">
                        <h4 className="font-semibold text-sm text-info dark:text-info mb-2">Technique</h4>
                        <p className="text-sm text-info dark:text-info leading-relaxed">{procedure.technique}</p>
                      </div>

                      <div className="grid gap-6 md:grid-cols-2">
                        <div className="bg-background/60 dark:bg-muted/60 backdrop-blur-sm rounded-lg p-4">
                          <h4 className="font-semibold text-sm mb-3 text-foreground">Indications:</h4>
                          <ul className="space-y-2">
                            {procedure.indications.slice(0, 3).map((indication, idx) => (
                              <li key={idx} className="text-sm flex items-start gap-3 text-foreground/90">
                                <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0" />
                                <span className="leading-relaxed">{indication}</span>
                              </li>
                            ))}
                          </ul>
                        </div>

                        <div className="bg-background/60 dark:bg-muted/60 backdrop-blur-sm rounded-lg p-4">
                          <h4 className="font-semibold text-sm mb-3 text-foreground">Advantages:</h4>
                          <ul className="space-y-2">
                            {procedure.advantages.slice(0, 3).map((advantage, idx) => (
                              <li key={idx} className="text-sm flex items-start gap-3 text-foreground/90">
                                <div className="w-2 h-2 bg-success rounded-full mt-2 flex-shrink-0" />
                                <span className="leading-relaxed">{advantage}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>

                      <div className="bg-background/60 dark:bg-muted/60 backdrop-blur-sm rounded-lg p-4">
                        <h4 className="font-semibold text-sm mb-3 text-foreground">Risks:</h4>
                        <ul className="space-y-2">
                          {procedure.risks.slice(0, 3).map((risk, idx) => (
                            <li key={idx} className="text-sm flex items-start gap-3 text-foreground/90">
                              <div className="w-2 h-2 bg-muted rounded-full mt-2 flex-shrink-0" />
                              <span className="leading-relaxed">{risk}</span>
                            </li>
                          ))}
                        </ul>
                      </div>

                      <div className="flex justify-between items-centre pt-4 border-t border-border/50">
                        <span className="text-sm text-muted-foreground font-medium">Success Rate:</span>
                        <span className="text-sm font-bold text-foreground">{procedure.successRate}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Medications */}
          <section className="py-16 bg-gradient-to-br from-background via-background/95 to-muted/30">
            <div className="container">
              <div className="text-centre mb-12">
                <h2 className="text-enhanced-heading text-3xl font-bold mb-4 text-foreground">{hemifacialSpasmData.medications.title}</h2>
                <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
                  {hemifacialSpasmData.medications.description}
                </p>
              </div>

              <div className="content-spacing">
                {hemifacialSpasmData.medications.categories.map((category, index) => (
                  <div key={index} className="medical-card/90 backdrop-blur-sm border border-border/50 rounded-xl p-8 shadow-xl hover:shadow-xl transition-all duration-300">
                    <h3 className="font-bold text-xl mb-6 text-foreground">{category.category}</h3>
                    <div className="grid gap-6 lg:grid-cols-2">
                      {category.medications.map((medication, idx) => (
                        <div key={idx} className="border-l-4 border-primary/40 pl-6 bg-background/60 dark:bg-muted/60 backdrop-blur-sm rounded-lg p-4">
                          <h4 className="font-bold text-lg mb-4 text-foreground">{medication.name}</h4>
                          <div className="content-spacing-sm">
                            <div className="bg-info/80 dark:bg-info/30 border border-info/70 dark:border-info/50 rounded-lg p-4">
                              <h5 className="font-semibold text-sm text-info dark:text-info mb-2">Mechanism</h5>
                              <p className="text-sm text-info dark:text-info leading-relaxed">{medication.mechanism}</p>
                            </div>

                            <div className="grid gap-4 md:grid-cols-2">
                              <div className="bg-muted/80 dark:bg-muted/60 rounded-lg p-3">
                                <h5 className="font-semibold text-sm mb-2 text-foreground">Dosage:</h5>
                                <p className="text-sm text-foreground/90 leading-relaxed">{medication.dosage}</p>
                              </div>
                              <div className="bg-muted/80 dark:bg-muted/60 rounded-lg p-3">
                                <h5 className="font-semibold text-sm mb-2 text-foreground">Effectiveness:</h5>
                                <p className="text-sm text-foreground/90 leading-relaxed">{medication.effectiveness}</p>
                              </div>
                            </div>

                            <div className="bg-info/80 dark:bg-info/30 border border-info/70 dark:border-info/50 rounded-lg p-4">
                              <h5 className="font-semibold text-sm mb-3 text-info dark:text-info">Side Effects:</h5>
                              <ul className="space-y-2">
                                {medication.sideEffects.slice(0, 3).map((effect, sIdx) => (
                                  <li key={sIdx} className="text-sm flex items-start gap-3 text-info dark:text-info">
                                    <div className="w-2 h-2 bg-info rounded-full mt-2 flex-shrink-0" />
                                    <span className="leading-relaxed">{effect}</span>
                                  </li>
                                ))}
                              </ul>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Living with Hemifacial Spasm */}
          <section className="py-16 bg-gradient-to-br from-muted/20 via-muted/30 to-muted/20 border-y border-border/50">
            <div className="container">
              <div className="text-centre mb-12">
                <h2 className="text-enhanced-heading text-3xl font-bold mb-4 text-foreground">{hemifacialSpasmData.livingWithHemifacialSpasm.title}</h2>
                <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
                  {hemifacialSpasmData.livingWithHemifacialSpasm.description}
                </p>
              </div>

              <div className="grid gap-8 md:grid-cols-3">
                {hemifacialSpasmData.livingWithHemifacialSpasm.sections.map((section, index) => (
                  <div key={index} className="medical-card/90 backdrop-blur-sm border border-border/50 rounded-xl p-6 shadow-xl hover:shadow-xl transition-all duration-300 hover:scale-105">
                    <h3 className="font-bold text-xl mb-4 text-foreground">{section.title}</h3>
                    <div className="space-y-3 mb-6">
                      {section.content.map((paragraph, idx) => (
                        <p key={idx} className="text-sm text-foreground/90 leading-relaxed">{paragraph}</p>
                      ))}
                    </div>
                    <div className="bg-background/60 dark:bg-muted/60 backdrop-blur-sm rounded-lg p-4">
                      <h4 className="font-semibold text-sm mb-3 text-foreground">Practical Tips:</h4>
                      <ul className="space-y-2">
                        {section.tips.map((tip, idx) => (
                          <li key={idx} className="text-sm text-foreground/90 flex items-start gap-3">
                            <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0" />
                            <span className="leading-relaxed">{tip}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Follow-up Care */}
          <section className="py-16 bg-gradient-to-br from-background via-background/95 to-muted/30">
            <div className="container">
              <div className="text-centre mb-12">
                <h2 className="text-enhanced-heading text-3xl font-bold mb-4 text-foreground">{hemifacialSpasmData.followUpCare.title}</h2>
                <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
                  {hemifacialSpasmData.followUpCare.description}
                </p>
              </div>

              <div className="grid gap-8 md:grid-cols-3">
                {hemifacialSpasmData.followUpCare.monitoring.map((timeframe, index) => (
                  <div key={index} className="medical-card/90 backdrop-blur-sm border border-border/50 rounded-xl p-6 shadow-xl hover:shadow-xl transition-all duration-300 hover:scale-105">
                    <h3 className="font-bold text-xl mb-4 text-foreground">{timeframe.timeframe}</h3>
                    <p className="text-sm text-foreground/90 mb-6 leading-relaxed">{timeframe.purpose}</p>
                    <div className="bg-background/60 dark:bg-muted/60 backdrop-blur-sm rounded-lg p-4">
                      <h4 className="font-semibold text-sm mb-3 text-foreground">Required Procedures:</h4>
                      <ul className="space-y-2">
                        {timeframe.procedures.map((procedure, idx) => (
                          <li key={idx} className="text-sm flex items-start gap-3 text-foreground/90">
                            <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0" />
                            <span className="leading-relaxed">{procedure}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Support Resources */}
          <section className="py-16 bg-gradient-to-br from-muted/20 via-muted/30 to-muted/20 border-y border-border/50">
            <div className="container">
              <div className="text-centre mb-12">
                <h2 className="text-enhanced-heading text-3xl font-bold mb-4 text-foreground">{hemifacialSpasmData.supportResources.title}</h2>
                <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
                  {hemifacialSpasmData.supportResources.description}
                </p>
              </div>

              <div className="grid gap-8 md:grid-cols-3">
                {hemifacialSpasmData.supportResources.resources.map((resourceCategory, index) => (
                  <div key={index} className="medical-card/90 backdrop-blur-sm border border-border/50 rounded-xl p-6 shadow-xl hover:shadow-xl transition-all duration-300 hover:scale-105">
                    <h3 className="font-bold text-xl mb-6 text-foreground">{resourceCategory.category}</h3>
                    <div className="content-spacing-sm">
                      {resourceCategory.items.map((item, idx) => (
                        <div key={idx} className="bg-background/60 dark:bg-muted/60 backdrop-blur-sm rounded-lg p-4 border-b border-border/30 last:border-b-0">
                          <h4 className="font-semibold mb-2 text-foreground">{item.name}</h4>
                          <p className="text-sm text-foreground/90 mb-2 leading-relaxed">{item.description}</p>
                          {item.contact && (
                            <p className="text-sm text-primary font-medium">{item.contact}</p>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Warning Signs */}
          <section className="py-16 bg-gradient-to-br from-info-light/80 via-info-light/60 to-info-light/80 dark:from-info-light/30 dark:via-info-light/20 dark:to-info-light/30 border-y border-border dark:border-border">
            <div className="container">
              <div className="text-centre mb-12">
                <h2 className="text-enhanced-heading text-3xl font-bold mb-4 text-foreground dark:text-foreground">{hemifacialSpasmData.warningSigns.title}</h2>
                <p className="text-lg text-foreground dark:text-foreground max-w-3xl mx-auto leading-relaxed">
                  {hemifacialSpasmData.warningSigns.description}
                </p>
              </div>

              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                {hemifacialSpasmData.warningSigns.emergencySigns.map((sign, index) => (
                  <div key={index} className="bg-background/90 dark:bg-muted/90 backdrop-blur-sm border border-border/70 dark:border-border rounded-xl p-6 shadow-xl hover:shadow-xl transition-all duration-300 hover:scale-105">
                    <h3 className="font-bold text-lg mb-3 text-foreground dark:text-foreground">{sign.sign}</h3>
                    <p className="text-sm text-foreground dark:text-foreground mb-4 leading-relaxed">{sign.description}</p>
                    <div className="bg-muted/80 dark:bg-muted/50 border border-border/70 dark:border-border rounded-lg p-4 mb-4">
                      <p className="text-sm font-semibold text-foreground dark:text-foreground leading-relaxed">{sign.action}</p>
                    </div>
                    <div className="flex justify-centre">
                      <span className={`text-xs px-3 py-1.5 rounded-full font-bold shadow-lg ${
                        sign.urgency === 'urgent' ? 'bg-muted text-primary-foreground' :
                        sign.urgency === 'prompt' ? 'bg-primary text-primary-foreground' :
                        'bg-primary text-primary-foreground'
                      }`}>
                        {sign.urgency.toUpperCase()}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>
        </main>
      </StandardPageLayout>
    </>
  );
};

export default HemifacialSpasm;
