import React, { useEffect } from 'react';
import { Helmet } from 'react-helmet-async';

import { BrainTumourTypes, TreatmentComparison, WarningSigns, SymptomAssessment } from '@/components/medical-conditions/brain-tumour';
import {
  ConditionHero,
  ConditionOverviewSection,
  ConditionQuickFacts
} from '@/components/medical-conditions/shared';
import StandardPageLayout from '@/components/StandardPageLayout';
import { brainTumourData } from '@/data/conditions/brainTumour';
import { useScrollToTop } from '@/hooks/useScrollToTop';

const BrainTumour: React.FC = () => {
  useScrollToTop();

  useEffect(() => {
    // Track page view for analytics
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('config', 'GA_MEASUREMENT_ID', {
        page_title: 'Brain Tumour Guide',
        page_location: window.location.href,
      });
    }
  }, []);

  return (
    <>
      <Helmet>
        <title>Brain Tumour: Comprehensive Patient Guide | miNEURO</title>
        <meta 
          name="description" 
          content="Complete guide to brain tumours: types, symptoms, diagnosis, and treatment options. Expert neurosurgical care with advanced surgical techniques and comprehensive patient support." 
        />
        <meta 
          name="keywords" 
          content="brain tumour, brain tumor, glioma, meningioma, neurosurgery, brain surgery, image-guided surgery, brain cancer treatment, Melbourne neurosurgeon" 
        />
        <meta name="author" content="Dr. Ales Aliashkevich" />
        <meta property="og:title" content="Brain Tumour: Comprehensive Patient Guide | miNEURO" />
        <meta 
          property="og:description" 
          content="Expert guide to brain tumours covering types, symptoms, diagnosis, and advanced treatment options including image-guided surgery and minimally invasive techniques." 
        />
        <meta property="og:type" content="article" />
        <meta property="og:url" content="https://mineuro.com.au/patient-resources/conditions/brain-tumour" />
        <meta property="og:image" content="https://mineuro.com.au/images/brain-conditions/brain-tumour-guide-og.jpg" />
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="Brain Tumour: Comprehensive Patient Guide" />
        <meta name="twitter:description" content="Complete guide to brain tumours with expert neurosurgical insights and treatment options." />
        <link rel="canonical" href="https://mineuro.com.au/patient-resources/conditions/brain-tumour" />
        
        {/* Structured Data for Medical Content */}
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "MedicalWebPage",
            "name": "Brain Tumour: Comprehensive Patient Guide",
            "description": "Complete guide to brain tumours: types, symptoms, diagnosis, and treatment options",
            "url": "https://mineuro.com.au/patient-resources/conditions/brain-tumour",
            "mainEntity": {
              "@type": "MedicalCondition",
              "name": "Brain Tumour",
              "alternateName": ["Brain Tumor", "Intracranial Neoplasm"],
              "description": "Abnormal growth of cells within the brain or central nervous system",
              "symptom": [
                "Headaches",
                "Seizures",
                "Cognitive changes",
                "Motor weakness",
                "Vision problems"
              ],
              "riskFactor": [
                "Age",
                "Genetic factors",
                "Radiation exposure",
                "Family history"
              ]
            },
            "author": {
              "@type": "Person",
              "name": "Dr. Ales Aliashkevich",
              "jobTitle": "Neurosurgeon",
              "affiliation": {
                "@type": "Organization",
                "name": "miNEURO Brain and Spine Surgery"
              }
            },
            "datePublished": "2024-01-01",
            "dateModified": new Date().toISOString().split('T')[0],
            "publisher": {
              "@type": "Organization",
              "name": "miNEURO Brain and Spine Surgery",
              "url": "https://mineuro.com.au"
            }
          })}
        </script>
      </Helmet>

      <StandardPageLayout 
        title="Brain Tumour - Comprehensive Guide" 
        showHeader={false}
      >
        <main className="flex-1 pt-20">
          {/* Hero Section */}
          <ConditionHero
            title={brainTumourData.hero.title}
            subtitle={brainTumourData.hero.subtitle}
            backgroundImage={brainTumourData.hero.backgroundImage}
            badge={brainTumourData.hero.badge}
            showAssessment={true}
            showBooking={true}
            assessmentLink="#assessment"
            bookingLink="/appointments"
          />

          {/* Quick Facts */}
          <ConditionQuickFacts facts={brainTumourData.quickFacts} />

          {/* Overview Section */}
          <ConditionOverviewSection
            title={brainTumourData.overview.title}
            description={brainTumourData.overview.description}
            keyPoints={brainTumourData.overview.keyPoints}
            imageSrc={brainTumourData.overview.imageSrc}
            imageAlt={brainTumourData.overview.imageAlt}
            imageCaption={brainTumourData.overview.imageCaption}
          />

          {/* Brain Tumour Types */}
          <BrainTumourTypes
            title={brainTumourData.types.title}
            description={brainTumourData.types.description}
            primaryTumours={brainTumourData.types.primaryTumours}
            secondaryTumours={brainTumourData.types.secondaryTumours}
          />

          {/* Symptom Assessment Tool */}
          <div id="assessment">
            <SymptomAssessment symptomCategories={brainTumourData.symptoms} />
          </div>

          {/* Warning Signs */}
          <WarningSigns
            title={brainTumourData.warningSigns.title}
            description={brainTumourData.warningSigns.description}
            urgentSigns={brainTumourData.warningSigns.urgentSigns}
          />

          {/* Treatment Modalities Comparison */}
          <TreatmentComparison
            treatments={brainTumourData.treatmentModalities.treatments}
            title={brainTumourData.treatmentModalities.title}
            description={brainTumourData.treatmentModalities.description}
          />

          {/* Surgical Options */}
          <TreatmentComparison
            treatments={brainTumourData.surgicalOptions.procedures.map(procedure => ({
              name: procedure.name,
              description: procedure.description,
              indications: procedure.candidacy,
              advantages: procedure.advantages,
              disadvantages: procedure.risks,
              successRate: procedure.outcomes,
              recoveryTime: "Varies by procedure complexity",
              sideEffects: procedure.risks
            }))}
            title={brainTumourData.surgicalOptions.title}
            description={brainTumourData.surgicalOptions.description}
          />

          {/* Precautions Section */}
          <section className="py-20 bg-gradient-to-br from-info-light/70 via-info-light/50 to-background dark:from-info-light/20 dark:via-info-light/10 dark:to-background border-y border-border/50">
            <div className="container">
              <div className="text-centre mb-16">
                <h2 className="text-enhanced-heading text-3xl lg:text-enhanced-heading text-4xl font-bold mb-6 text-foreground">{brainTumourData.precautions.title}</h2>
                <p className="text-lg text-foreground/80 max-w-4xl mx-auto leading-relaxed font-medium">
                  {brainTumourData.precautions.description}
                </p>
              </div>

              <div className="grid gap-10 md:grid-cols-2">
                {brainTumourData.precautions.categories.map((category, index) => (
                  <div key={index} className="medical-card/90 backdrop-blur-sm rounded-xl p-8 shadow-xl border border-border/50 hover:shadow-xl transition-all duration-300 hover:scale-105 hover:border-primary/30">
                    <h3 className="flex items-centre gap-4 font-bold text-xl mb-6 text-foreground">
                      <div className="p-2 rounded-lg bg-primary/10 border border-primary/20">
                        <category.icon className="h-6 w-6 text-primary" />
                      </div>
                      {category.category}
                    </h3>
                    <div className="content-spacing">
                      {category.precautions.map((precaution, idx) => (
                        <div key={idx} className="border-l-4 border-primary/40 pl-6 py-3 bg-primary/5 rounded-r-lg">
                          <h4 className="font-bold mb-2 text-foreground text-base">{precaution.title}</h4>
                          <p className="text-base text-foreground/80 leading-relaxed">{precaution.description}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Living with Brain Tumour */}
          <section className="py-20 bg-gradient-to-br from-background via-background/95 to-muted/30">
            <div className="container">
              <div className="text-centre mb-16">
                <h2 className="text-enhanced-heading text-3xl lg:text-enhanced-heading text-4xl font-bold mb-6 text-foreground">{brainTumourData.livingWithBrainTumour.title}</h2>
                <p className="text-lg text-foreground/80 max-w-4xl mx-auto leading-relaxed font-medium">
                  {brainTumourData.livingWithBrainTumour.description}
                </p>
              </div>

              <div className="grid gap-10 md:grid-cols-3">
                {brainTumourData.livingWithBrainTumour.sections.map((section, index) => (
                  <div key={index} className="medical-card/90 backdrop-blur-sm rounded-xl p-8 shadow-xl border border-border/50 hover:shadow-xl transition-all duration-300 hover:scale-105 hover:border-primary/30">
                    <h3 className="font-bold text-xl mb-6 text-foreground">{section.title}</h3>
                    <div className="space-y-4 mb-6">
                      {section.content.map((paragraph, idx) => (
                        <p key={idx} className="text-base text-foreground/80 leading-relaxed">{paragraph}</p>
                      ))}
                    </div>
                    <div className="content-spacing-sm">
                      <h4 className="font-bold text-base text-foreground">Practical Tips:</h4>
                      <ul className="space-y-3">
                        {section.tips.map((tip, idx) => (
                          <li key={idx} className="text-base text-foreground/80 flex items-start gap-3">
                            <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0" />
                            {tip}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Support Resources */}
          <section className="py-20 bg-gradient-to-br from-info-light/70 via-info-light/50 to-background dark:from-info-light/20 dark:via-info-light/10 dark:to-background border-y border-border/50">
            <div className="container">
              <div className="text-center mb-16">
                <h2 className="text-enhanced-heading text-3xl lg:text-enhanced-heading text-4xl font-bold mb-6 text-foreground">{brainTumourData.supportResources.title}</h2>
                <p className="text-lg text-foreground/80 max-w-4xl mx-auto leading-relaxed font-medium">
                  {brainTumourData.supportResources.description}
                </p>
              </div>

              <div className="grid gap-10 md:grid-cols-3">
                {brainTumourData.supportResources.resources.map((resourceCategory, index) => (
                  <div key={index} className="medical-card/90 backdrop-blur-sm rounded-xl p-8 shadow-xl border border-border/50 hover:shadow-xl transition-all duration-300 hover:scale-105 hover:border-primary/30">
                    <h3 className="font-bold text-xl mb-6 text-foreground">{resourceCategory.category}</h3>
                    <div className="content-spacing">
                      {resourceCategory.items.map((item, idx) => (
                        <div key={idx} className="border-b border-border/30 pb-4 last:border-b-0 last:pb-0">
                          <h4 className="font-bold mb-2 text-foreground text-base">{item.name}</h4>
                          <p className="text-base text-foreground/80 mb-2 leading-relaxed">{item.description}</p>
                          {item.contact && (
                            <p className="text-sm text-primary font-semibold">{item.contact}</p>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>
        </main>
      </StandardPageLayout>
    </>
  );
};

export default BrainTumour;
