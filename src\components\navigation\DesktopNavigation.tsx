import React from 'react';
import { Link } from 'react-router-dom';

import ThemeToggle from '@/components/ThemeToggle';
import { Button } from '@/components/ui/button';
import { NavLink, NavSubmenuItem } from '@/data/navigation/navigationData';

interface DesktopNavigationProps {
  navLinks: NavLink[];
  ctaButton: { text: string; path: string };
  activeSubmenu: string | null;
  onSubmenuEnter: (name: string) => void;
  onSubmenuLeave: () => void;
  onKeyDown: (linkName: string) => (e: React.KeyboardEvent) => void;
  onCloseSubmenu: () => void;
}

const DesktopNavigation: React.FC<DesktopNavigationProps> = ({
  navLinks,
  ctaButton,
  activeSubmenu,
  onSubmenuEnter,
  onSubmenuLeave,
  onKeyDown,
  onCloseSubmenu
}) => {
  return (
    <div className="hidden lg:flex items-center justify-between w-full max-w-4xl mx-auto">
      {/* Desktop Navigation Links */}
      <ul className="flex items-center space-x-8" role="menubar">
        {navLinks?.map(link => (
          <li key={link.name} className={`relative nav-item-with-submenu ${activeSubmenu === link.name ? 'active' : ''}`} role="none">
            {link.submenu ? (
              <div className="relative">
                <Link
                  to={link.path}
                  className="text-enhanced-link font-semibold transition-all duration-200 hover:text-primary flex items-center text-sm lg:text-base whitespace-nowrap py-2 px-1"
                  onMouseEnter={() => onSubmenuEnter(link.name)}
                  onMouseLeave={onSubmenuLeave}
                  onKeyDown={onKeyDown(link.name)}
                  aria-expanded={activeSubmenu === link.name}
                  aria-haspopup="true"
                  role="menuitem"
                  tabIndex={0}
                >
                  {link.name}
                  <span className="ml-1 inline-block text-sm">▾</span>
                </Link>

                {/* Dropdown for items with submenu */}
                <div
                  className={`absolute left-0 mt-2 w-56 rounded-md shadow-lg bg-card ring-1 ring-border transition-opacity duration-200 z-50 ${
                    activeSubmenu === link.name ? 'opacity-100 visible' : 'opacity-0 invisible'
                  }`}
                  role="menu"
                  aria-orientation="vertical"
                  aria-labelledby={`${link.name.toLowerCase()}-menu`}
                  onMouseEnter={() => onSubmenuEnter(link.name)}
                  onMouseLeave={onSubmenuLeave}
                >
                  <div className="py-1">
                    {link.submenu?.map((subItem: NavSubmenuItem) => (
                      <Link
                        key={subItem.name}
                        to={subItem.path}
                        className="block px-4 py-2 text-sm text-foreground hover:bg-muted hover:text-primary"
                        role="menuitem"
                        tabIndex={activeSubmenu === link.name ? 0 : -1}
                        onClick={onCloseSubmenu}
                      >
                        {subItem.name}
                      </Link>
                    ))}
                  </div>
                </div>
              </div>
            ) : (
              <Link
                to={link.path}
                className="font-medium transition-all duration-200 hover:text-primary text-sm lg:text-base whitespace-nowrap py-2 px-1"
                role="menuitem"
                tabIndex={0}
              >
                {link.name}
              </Link>
            )}
          </li>
        ))}
      </ul>

      {/* Right side actions */}
      <div className="flex items-center space-x-4">
        <ThemeToggle />
        <Button asChild className="btn-primary px-6 py-2">
          <Link to={ctaButton.path}>{ctaButton.text}</Link>
        </Button>
      </div>
    </div>
  );
};

DesktopNavigation.displayName = 'DesktopNavigation';

export default DesktopNavigation;
