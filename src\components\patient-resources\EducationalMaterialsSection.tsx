import React from 'react';

import { Button } from '@/components/ui/button';
import { EducationalMaterial } from '@/data/patient-resources/educationalMaterials';

interface EducationalMaterialsSectionProps {
  materials: EducationalMaterial[];
  title: string;
}

const EducationalMaterialsSection: React.FC<EducationalMaterialsSectionProps> = ({
  materials, 
  title 
}) => {
  return (
    <section className="py-16 bg-muted/30">
      <div className="container max-w-7xl">
        <div className="text-center mb-12">
          <h2 className="text-enhanced-heading text-3xl font-bold mb-4 text-foreground">{title}</h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Download comprehensive guides and resources to support your healthcare journey.
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-6xl mx-auto">
          {materials?.map((material, index) => {
            const IconComponent = material.icon;
            return (
              <div 
                key={material.id} 
                className="medical-card/50 backdrop-blur-sm border border-border/50 p-6 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 animate-fade-in" 
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <div className="flex justify-between items-start mb-4">
                  <h3 className="text-lg font-semibold text-foreground flex-1 mr-2">{material.title}</h3>
                  <div className="p-2 rounded-lg bg-primary/10 flex-shrink-0">
                    <IconComponent className="h-5 w-5 text-primary" />
                  </div>
                </div>
                <p className="text-muted-foreground mb-4 text-sm leading-relaxed">{material.description}</p>
                <Button asChild variant="ghost" className="text-primary p-0 h-auto hover:bg-primary/10">
                  <a
                    href={material.link}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-centre"
                  >
                    View Guide
                  </a>
                </Button>
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
};

EducationalMaterialsSection.displayName = 'EducationalMaterialsSection';

export default EducationalMaterialsSection;
