import React from 'react';
import { Link } from 'react-router-dom';

import { Button } from '@/components/ui/button';

interface ContactSectionProps {
  translations: {
    contactInfo?: {
      title?: string;
      description?: string;
    };
  };
}

const ContactSection: React.FC<ContactSectionProps> = ({ translations }) => {
  return (
    <section className="py-16 bg-primary/5">
      <div className="container">
        <div className="text-centre mb-8">
          <h2 className="text-enhanced-heading text-3xl font-bold mb-4">{translations.contactInfo?.title || 'Contact Information'}</h2>
          <p className="text-muted-foreground max-w-3xl mx-auto mb-8">
            {translations.contactInfo?.description || 'Get in touch to arrange your medicolegal assessment or for more information about our services.'}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
          <div className="text-centre">
            <div className="bg-primary/10 w-16 h-16 rounded-full flex items-centre justify-centre mx-auto mb-4">
              <span className="text-primary text-2xl">📞</span>
            </div>
            <h3 className="text-xl font-semibold mb-2 text-primary">Phone</h3>
            <p className="text-muted-foreground mb-2">+61 3 9826 0288</p>
            <p className="text-muted-foreground text-sm">Call during business hours for appointments and inquiries</p>
          </div>

          <div className="text-centre">
            <div className="bg-primary/10 w-16 h-16 rounded-full flex items-centre justify-centre mx-auto mb-4">
              <span className="text-primary text-2xl">✉️</span>
            </div>
            <h3 className="text-xl font-semibold mb-2 text-primary">Email</h3>
            <p className="text-muted-foreground mb-2"><EMAIL></p>
            <p className="text-muted-foreground text-sm">Email for general inquiries and appointment requests</p>
          </div>

          <div className="text-center">
            <div className="bg-primary/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-primary text-2xl">📍</span>
            </div>
            <h3 className="text-xl font-semibold mb-2 text-primary">Address</h3>
            <p className="text-muted-foreground mb-2">Level 6, 142 Toorak Road, South Yarra VIC 3141</p>
            <p className="text-muted-foreground text-sm">Primary consultation location</p>
          </div>
        </div>

        <div className="text-center">
          <Button asChild size="lg">
            <Link to="/appointments">Schedule Assessment</Link>
          </Button>
        </div>
      </div>
    </section>
  );
};

ContactSection.displayName = 'ContactSection';

export default ContactSection;
