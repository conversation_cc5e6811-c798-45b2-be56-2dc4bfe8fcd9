import React from 'react';

import CTASection from '@/components/CTASection';
import HeroSection from '@/components/HeroSection';
import AppointmentsSection from '@/components/home/<USER>';
import ArthroplastySection from '@/components/home/<USER>';
import ExpertiseShowcase from '@/components/home/<USER>';
import FeedbackSection from '@/components/home/<USER>';
import ImageGuidedSurgerySection from '@/components/home/<USER>';
import MinimallyInvasiveSection from '@/components/home/<USER>';
import ServicesOverview from '@/components/home/<USER>';
import TreatmentDecisionsSection from '@/components/home/<USER>';
import WelcomeSection from '@/components/home/<USER>';
import WhenLessIsMoreSection from '@/components/home/<USER>';
import IndependentReviewsSection from '@/components/IndependentReviewsSection';
import { StandardErrorBoundary, SimpleErrorFallback } from '@/components/shared';
import StandardPageLayout from '@/components/StandardPageLayout';
import { useHomepageState } from '@/hooks/useHomepageState';

/**
 * Refactored Homepage Component
 * Streamlined version using modular components and data management hooks
 * Preserves all original content and functionality from the original Index.tsx
 * Now uses Foundation Phase shared components and enhanced state management
 */
const Index: React.FC = () => {
  const {
    deviceInfo,
    homepageData,
    translations,
    seoData,
    isLoading,
    errorMessage,
    isReady,
    shouldShowError,
    retryInitialization,
  } = useHomepageState();

  // Show error state if initialization failed
  if (shouldShowError) {
    return (
      <StandardPageLayout pageType="home" showHeader={false}>
        <SimpleErrorFallback
          error={new Error(errorMessage)}
          resetError={retryInitialization}
          message="Failed to load homepage. Please try again."
        />
      </StandardPageLayout>
    );
  }

  // Show loading state
  if (isLoading || !isReady) {
    return (
      <StandardPageLayout pageType="home" showHeader={false}>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-enhanced-body">Loading...</p>
          </div>
        </div>
      </StandardPageLayout>
    );
  }

  return (
    <StandardErrorBoundary componentName="Homepage" showErrorDetails={false}>
      <StandardPageLayout pageType="home" seoData={seoData} showHeader={false}>
        <div className="flex-1" id="main-content">
          {/* Hero Section */}
          <StandardErrorBoundary componentName="HeroSection">
            <HeroSection />
          </StandardErrorBoundary>

          {/* Welcome Section */}
          <StandardErrorBoundary componentName="WelcomeSection">
            <WelcomeSection
              deviceInfo={deviceInfo}
              getTranslation={translations.getTranslation}
            />
          </StandardErrorBoundary>

          {/* Comprehensive Services Overview Section */}
          <StandardErrorBoundary componentName="ServicesOverview">
            <ServicesOverview
              deviceInfo={deviceInfo}
              services={homepageData.services}
            />
          </StandardErrorBoundary>

          {/* Expertise Section */}
          <StandardErrorBoundary componentName="ExpertiseShowcase">
            <ExpertiseShowcase
              deviceInfo={deviceInfo}
              expertise={homepageData.expertise}
            />
          </StandardErrorBoundary>

          {/* Why Minimally-Invasive Surgery Section */}
          <StandardErrorBoundary componentName="MinimallyInvasiveSection">
            <MinimallyInvasiveSection
              principles={homepageData.misPhilosophyPrinciples}
            />
          </StandardErrorBoundary>

          {/* When Less is More Section */}
          <StandardErrorBoundary componentName="WhenLessIsMoreSection">
            <WhenLessIsMoreSection
              benefits={homepageData.whenLessIsMoreBenefits}
              principles={homepageData.misPhilosophyPrinciples}
            />
          </StandardErrorBoundary>

          {/* Cervical and Lumbar Arthroplasty Section */}
          <StandardErrorBoundary componentName="ArthroplastySection">
            <ArthroplastySection />
          </StandardErrorBoundary>

          {/* Image-Guided and Robotic Surgery Section */}
          <StandardErrorBoundary componentName="ImageGuidedSurgerySection">
            <ImageGuidedSurgerySection
              keyBenefits={homepageData.imageGuidedBenefits}
              patientOutcomes={homepageData.patientOutcomes}
            />
          </StandardErrorBoundary>

          {/* Treatment Decisions Section */}
          <StandardErrorBoundary componentName="TreatmentDecisionsSection">
            <TreatmentDecisionsSection
              content={homepageData.treatmentDecisionsContent}
            />
          </StandardErrorBoundary>

          {/* Independent Reviews Section */}
          <StandardErrorBoundary componentName="IndependentReviewsSection">
            <IndependentReviewsSection />
          </StandardErrorBoundary>

          {/* Comprehensive Appointments Section */}
          <StandardErrorBoundary componentName="AppointmentsSection">
            <AppointmentsSection
              consultationSteps={homepageData.consultationSteps}
              preparation={homepageData.consultationPreparation}
            />
          </StandardErrorBoundary>

          {/* Feedback and Complaints Section */}
          <StandardErrorBoundary componentName="FeedbackSection">
            <FeedbackSection
              sections={homepageData.feedbackSections}
            />
          </StandardErrorBoundary>

          {/* CTA Section */}
          <StandardErrorBoundary componentName="CTASection">
            <CTASection className="py-24" />
          </StandardErrorBoundary>
        </div>
      </StandardPageLayout>
    </StandardErrorBoundary>
  );
};

Index.displayName = 'Index';

export default Index;
