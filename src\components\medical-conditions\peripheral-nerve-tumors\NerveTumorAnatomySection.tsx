import { 
  Zap, 
  Target, 
  MapPin,
  Microscope,
  Eye,
  Shield,
  Layers,
  Circle
} from "lucide-react";
import React, { useState } from 'react';

import SafeImage from '@/components/SafeImage';
import { Badge } from '@/components/ui/badge';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface TumorType {
  id: string;
  name: string;
  description: string;
  characteristics: string[];
  cellOrigin: string;
  malignancyRisk: string;
  treatmentApproach: string;
}

interface AnatomyPoint {
  id: string;
  title: string;
  description: string;
  tumorType: string;
  location: { x: number; y: number };
}

interface NerveTumorAnatomySectionProps {
  className?: string;
}

const anatomyData = {
  title: "Peripheral Nerve Tumor Anatomy and Classification",
  subtitle: "Understanding the cellular origins, anatomical relationships, and characteristics of peripheral nerve tumors",
  
  tumorTypes: [
    {
      id: 'schwannoma',
      name: '<PERSON><PERSON>nn<PERSON>',
      description: 'Benign tumor arising from Schwann cells, the most common peripheral nerve tumor',
      characteristics: [
        'Well-encapsulated and eccentric to nerve',
        'Can be enucleated without nerve sacrifice',
        'Usually solitary (except in schwannomatosis)',
        'Slow-growing and typically asymptomatic',
        'Strong S-100 protein immunoreactivity',
        'Antoni A and B tissue patterns on histology'
      ],
      cellOrigin: 'Schwann cells (myelin-producing cells)',
      malignancyRisk: '<1% risk of malignant transformation',
      treatmentApproach: 'Microsurgical enucleation with nerve preservation'
    },
    {
      id: 'neurofibroma',
      name: 'Neurofibroma',
      description: 'Benign tumor involving nerve fascicles, often associated with neurofibromatosis',
      characteristics: [
        'Fusiform enlargement of nerve',
        'Involves multiple nerve fascicles',
        'Cannot be completely separated from nerve',
        'May be solitary or multiple (NF1)',
        'Mixed cellular composition',
        'Positive for S-100 and neurofilament'
      ],
      cellOrigin: 'Schwann cells, perineural cells, and fibroblasts',
      malignancyRisk: '2-5% risk in NF1 patients (higher in plexiform type)',
      treatmentApproach: 'Intraneural dissection or nerve resection with reconstruction'
    },
    {
      id: 'neuroma',
      name: 'Traumatic Neuroma',
      description: 'Non-neoplastic proliferation of nerve tissue following injury',
      characteristics: [
        'Painful, tender mass at injury site',
        'Disorganised nerve regeneration',
        'Scar tissue incorporation',
        'Positive Tinel\'s sign',
        'Associated with previous trauma or surgery',
        'May cause significant functional impairment'
      ],
      cellOrigin: 'Regenerating axons and supporting tissue',
      malignancyRisk: 'No malignant potential',
      treatmentApproach: 'Excision with nerve reconstruction or neuroma management techniques'
    },
    {
      id: 'mpnst',
      name: 'Malignant Peripheral Nerve Sheath Tumor (MPNST)',
      description: 'Rare malignant tumor with aggressive behaviour and poor prognosis',
      characteristics: [
        'Rapid growth and infiltrative pattern',
        'Loss of nerve function',
        'Associated with NF1 in 50% of cases',
        'High metastatic potential',
        'Requires wide surgical margins',
        'Poor response to conventional chemotherapy'
      ],
      cellOrigin: 'Malignant transformation of Schwann cells',
      malignancyRisk: 'Malignant by definition',
      treatmentApproach: 'Wide excision with adjuvant radiation therapy'
    }
  ],

  nerveAnatomy: [
    'Peripheral nerves consist of axons surrounded by supporting cells',
    'Schwann cells produce myelin sheaths around larger axons',
    'Endoneurium surrounds individual nerve fibres',
    'Perineurium groups fibres into fascicles',
    'Epineurium provides outer protective covering',
    'Blood-nerve barrier maintains nerve microenvironment'
  ],

  tumorLocations: [
    'Upper extremity nerves (median, ulnar, radial) - 40%',
    'Spinal nerve roots and plexuses - 25%',
    'Lower extremity nerves (sciatic, peroneal, tibial) - 20%',
    'Cranial nerves (especially acoustic nerve) - 10%',
    'Sympathetic chain and autonomic nerves - 5%'
  ],

  anatomyPoints: [
    {
      id: 'schwann-cell-layer',
      title: 'Schwann Cell Layer',
      description: 'Myelin-producing cells that can give rise to schwannomas',
      tumorType: 'Origin of schwannomas',
      location: { x: 25, y: 30 }
    },
    {
      id: 'nerve-fascicle',
      title: 'Nerve Fascicle',
      description: 'Bundle of nerve fibres that can be involved in neurofibromas',
      tumorType: 'Site of neurofibroma development',
      location: { x: 50, y: 45 }
    },
    {
      id: 'epineurium',
      title: 'Epineurium',
      description: 'Outer connective tissue layer of peripheral nerves',
      tumorType: 'Provides surgical plane for tumor removal',
      location: { x: 75, y: 35 }
    },
    {
      id: 'injury-site',
      title: 'Previous Injury Site',
      description: 'Location where traumatic neuromas typically develop',
      tumorType: 'Neuroma formation site',
      location: { x: 40, y: 70 }
    }
  ]
};

const NerveTumorAnatomySection: React.FC<NerveTumorAnatomySectionProps> = ({ className }) => {
  const deviceInfo = useDeviceDetection();
  const [selectedPoint, setSelectedPoint] = useState<string | null>(null);
  const [selectedTumorType, setSelectedTumorType] = useState<string>('schwannoma');

  return (
    <section className={cn(
      "section-background-alt border-y border-border/50",
      deviceInfo.isMobile ? "py-16" : "py-24",
      className
    )}>
      <div className="container">
        {/* Section Header */}
        <div className="text-centre mb-20">
          <Badge variant="info" className="mb-6">
            <Microscope className="w-4 h-4 mr-2" />
            Tumor Anatomy
          </Badge>
          <h2 className={cn(
            "font-bold text-foreground mb-8 leading-tight",
            deviceInfo.isMobile ? "text-2xl" : "text-3xl lg:text-4xl"
          )}>
            {anatomyData.title}
          </h2>
          <p className={cn(
            "text-foreground/80 max-w-4xl mx-auto leading-relaxed font-medium",
            deviceInfo.isMobile ? "text-base" : "text-lg"
          )}>
            {anatomyData.subtitle}
          </p>
        </div>

        {/* Tumor Type Tabs */}
        <Tabs value={selectedTumorType} onValueChange={setSelectedTumorType} className="w-full">
          <TabsList className={cn(
            "grid w-full mb-12",
            deviceInfo.isMobile ? "grid-cols-2 h-auto" : "grid-cols-4 h-14"
          )}>
            {anatomyData.tumorTypes.map((tumorType) => (
              <TabsTrigger 
                key={tumorType.id} 
                value={tumorType.id}
                className={cn(
                  "flex items-centre gap-2 font-medium",
                  deviceInfo.isMobile ? "flex-col py-3 px-2 text-xs" : "text-sm"
                )}
              >
                <Circle className={cn(deviceInfo.isMobile ? "w-4 h-4" : "w-5 h-5")} />
                <span className={deviceInfo.isMobile ? "text-centre" : ""}>
                  {tumorType.name}
                </span>
              </TabsTrigger>
            ))}
          </TabsList>

          {/* Tumor Type Content */}
          {anatomyData.tumorTypes.map((tumorType) => (
            <TabsContent key={tumorType.id} value={tumorType.id} className="space-y-8">
              {/* Tumor Type Overview */}
              <Card className="medical-card">
                <CardHeader>
                  <CardTitle className="text-enhanced-heading flex items-centre gap-3">
                    <Circle className="w-5 h-5 text-primary" />
                    {tumorType.name}
                  </CardTitle>
                  <p className="text-enhanced-body">{tumorType.description}</p>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Key Information Grid */}
                  <div className={cn(
                    "grid gap-4",
                    deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-3"
                  )}>
                    <div className="bg-info/5 border border-info/20 rounded-lg p-4">
                      <Microscope className="w-5 h-5 text-info mb-2" />
                      <h5 className="text-enhanced-caption font-medium">Cell Origin</h5>
                      <p className="text-enhanced-body text-sm">{tumorType.cellOrigin}</p>
                    </div>
                    <div className="bg-success/5 border border-success/20 rounded-lg p-4">
                      <Shield className="w-5 h-5 text-success mb-2" />
                      <h5 className="text-enhanced-caption font-medium">Malignancy Risk</h5>
                      <p className="text-enhanced-body text-sm">{tumorType.malignancyRisk}</p>
                    </div>
                    <div className="bg-primary/5 border border-primary/20 rounded-lg p-4">
                      <Target className="w-5 h-5 text-primary mb-2" />
                      <h5 className="text-enhanced-caption font-medium">Treatment</h5>
                      <p className="text-enhanced-body text-sm">{tumorType.treatmentApproach}</p>
                    </div>
                  </div>

                  {/* Characteristics */}
                  <div>
                    <h4 className="text-enhanced-subheading font-semibold mb-3 flex items-centre gap-2">
                      <Layers className="w-4 h-4 text-primary" />
                      Key Characteristics
                    </h4>
                    <ul className="space-y-2">
                      {tumorType.characteristics.map((characteristic, index) => (
                        <li key={index} className="flex items-start gap-2">
                          <div className="w-1.5 h-1.5 rounded-full bg-primary mt-2 flex-shrink-0" />
                          <span className="text-enhanced-body text-sm">{characteristic}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          ))}
        </Tabs>

        {/* Interactive Anatomy Diagram */}
        <Card className="medical-card mt-12">
          <CardHeader>
            <CardTitle className="text-enhanced-heading flex items-center gap-3">
              <Eye className="w-5 h-5 text-primary" />
              Interactive Nerve Anatomy
            </CardTitle>
            <p className="text-enhanced-body">
              Click on the anatomical points below to learn about nerve structures and tumor development sites.
            </p>
          </CardHeader>
          <CardContent>
            <div className={cn(
              "grid gap-8",
              deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-1 lg:grid-cols-2"
            )}>
              {/* Anatomy Image with Interactive Points */}
              <div className="relative">
                <SafeImage
                  src="https://images.unsplash.com/photo-**********-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80"
                  alt="Peripheral nerve anatomy and tumor locations"
                  className="w-full h-auto rounded-lg"
                  fallbackSrc="https://images.pexels.com/photos/3825527/pexels-photo-3825527.jpeg?auto=compress&cs=tinysrgb&w=600"
                />
                
                {/* Interactive Points */}
                {anatomyData.anatomyPoints.map((point) => (
                  <button
                    key={point.id}
                    onClick={() => setSelectedPoint(selectedPoint === point.id ? null : point.id)}
                    className={cn(
                      "absolute w-4 h-4 rounded-full border-2 transition-all duration-200",
                      selectedPoint === point.id
                        ? "bg-primary border-primary scale-125 shadow-lg"
                        : "bg-background border-primary hover:scale-110 hover:bg-primary/10"
                    )}
                    style={{
                      left: `${point.location.x}%`,
                      top: `${point.location.y}%`,
                      transform: 'translate(-50%, -50%)'
                    }}
                    aria-label={`View information about ${point.title}`}
                  />
                ))}
              </div>

              {/* Anatomy Point Details */}
              <div className="space-y-4">
                {selectedPoint ? (
                  (() => {
                    const point = anatomyData.anatomyPoints.find(p => p.id === selectedPoint);
                    return point ? (
                      <Card className="medical-card border-l-4 border-l-primary">
                        <CardHeader>
                          <CardTitle className="text-enhanced-subheading">{point.title}</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          <div>
                            <h4 className="text-enhanced-caption font-medium mb-2">Description:</h4>
                            <p className="text-enhanced-body text-sm">{point.description}</p>
                          </div>
                          <div>
                            <h4 className="text-enhanced-caption font-medium mb-2">Tumor Relevance:</h4>
                            <p className="text-enhanced-body text-sm">{point.tumorType}</p>
                          </div>
                        </CardContent>
                      </Card>
                    ) : null;
                  })()
                ) : (
                  <Card className="medical-card">
                    <CardContent className="text-centre py-8">
                      <MapPin className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                      <p className="text-enhanced-body">
                        Click on the anatomical points in the image to learn about nerve structures and tumor development sites.
                      </p>
                    </CardContent>
                  </Card>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Nerve Anatomy Overview */}
        <div className={cn(
          "grid gap-8 mt-12",
          deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-1 md:grid-cols-2"
        )}>
          <Card className="medical-card">
            <CardHeader>
              <CardTitle className="text-enhanced-heading flex items-centre gap-3">
                <Zap className="w-5 h-5 text-info" />
                Peripheral Nerve Structure
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-3">
                {anatomyData.nerveAnatomy.map((item, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <Layers className="w-4 h-4 text-info mt-0.5 flex-shrink-0" />
                    <span className="text-enhanced-body text-sm">{item}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>

          <Card className="medical-card">
            <CardHeader>
              <CardTitle className="text-enhanced-heading flex items-centre gap-3">
                <MapPin className="w-5 h-5 text-success" />
                Common Tumor Locations
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-3">
                {anatomyData.tumorLocations.map((location, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <Target className="w-4 h-4 text-success mt-0.5 flex-shrink-0" />
                    <span className="text-enhanced-body text-sm">{location}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
};

export default NerveTumorAnatomySection;
