import React, { useEffect } from 'react';
import { Helmet } from 'react-helmet-async';

import {
  MeningiomaAnatomySection,
  MeningiomaTreatmentComparison,
  MeningiomaLocationsSection,
  MeningiomaSymptomAssessment
} from '@/components/medical-conditions/cerebral-meningioma';
import {
  ConditionHero,
  ConditionOverviewSection,
  ConditionQuickFacts
} from '@/components/medical-conditions/shared';
import StandardPageLayout from '@/components/StandardPageLayout';
import { cerebralMeningiomaData } from '@/data/conditions/cerebralMeningioma';
import { useScrollToTop } from '@/hooks/useScrollToTop';

const CerebralMeningioma: React.FC = () => {
  useScrollToTop();

  useEffect(() => {
    // Track page view for analytics
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('config', 'GA_MEASUREMENT_ID', {
        page_title: 'Cerebral Meningioma Guide',
        page_location: window.location.href,
      });
    }
  }, []);

  return (
    <>
      <Helmet>
        <title>Cerebral Meningioma: Comprehensive Patient Guide | miNEURO</title>
        <meta 
          name="description" 
          content="Complete guide to cerebral meningiomas: causes, symptoms, diagnosis, and treatment options. Expert neurosurgical care with advanced surgical techniques and comprehensive monitoring." 
        />
        <meta 
          name="keywords" 
          content="cerebral meningioma, brain tumour, meningioma surgery, stereotactic radiosurgery, brain tumour treatment, neurosurgery, Melbourne neurosurgeon" 
        />
        <meta name="author" content="Dr. Ales Aliashkevich" />
        <meta property="og:title" content="Cerebral Meningioma: Comprehensive Patient Guide | miNEURO" />
        <meta 
          property="og:description" 
          content="Expert guide to cerebral meningiomas covering causes, symptoms, diagnosis, and advanced treatment options including surgical and radiation therapy interventions." 
        />
        <meta property="og:type" content="article" />
        <meta property="og:url" content="https://mineuro.com.au/patient-resources/conditions/cerebral-meningioma" />
        <meta property="og:image" content="https://mineuro.com.au/images/neurological-conditions/cerebral-meningioma-guide-og.jpg" />
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="Cerebral Meningioma: Comprehensive Patient Guide" />
        <meta name="twitter:description" content="Complete guide to cerebral meningiomas with expert neurosurgical insights and treatment options." />
        <link rel="canonical" href="https://mineuro.com.au/patient-resources/conditions/cerebral-meningioma" />
        
        {/* Structured Data for Medical Content */}
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "MedicalWebPage",
            "name": "Cerebral Meningioma: Comprehensive Patient Guide",
            "description": "Complete guide to cerebral meningiomas: causes, symptoms, diagnosis, and treatment options",
            "url": "https://mineuro.com.au/patient-resources/conditions/cerebral-meningioma",
            "mainEntity": {
              "@type": "MedicalCondition",
              "name": "Cerebral Meningioma",
              "alternateName": ["Brain Meningioma", "Intracranial Meningioma"],
              "description": "Tumour arising from the meninges, the protective membranes surrounding the brain",
              "symptom": [
                "Seizures",
                "Headaches",
                "Focal neurological deficits",
                "Vision changes",
                "Cognitive changes"
              ],
              "riskFactor": [
                "Female gender",
                "Age over 50",
                "Radiation exposure",
                "Hormonal factors"
              ]
            },
            "author": {
              "@type": "Person",
              "name": "Dr. Ales Aliashkevich",
              "jobTitle": "Neurosurgeon",
              "affiliation": {
                "@type": "Organization",
                "name": "miNEURO Brain and Spine Surgery"
              }
            },
            "datePublished": "2024-01-01",
            "dateModified": new Date().toISOString().split('T')[0],
            "publisher": {
              "@type": "Organization",
              "name": "miNEURO Brain and Spine Surgery",
              "url": "https://mineuro.com.au"
            }
          })}
        </script>
      </Helmet>

      <StandardPageLayout 
        title="Cerebral Meningioma - Comprehensive Guide" 
        showHeader={false}
      >
        <main className="flex-1 pt-20">
          {/* Hero Section */}
          <ConditionHero
            title={cerebralMeningiomaData.hero.title}
            subtitle={cerebralMeningiomaData.hero.subtitle}
            backgroundImage={cerebralMeningiomaData.hero.backgroundImage}
            badge={cerebralMeningiomaData.hero.badge}
            showAssessment={true}
            showBooking={true}
            assessmentLink="#symptom-assessment"
            bookingLink="/appointments"
          />

          {/* Quick Facts */}
          <ConditionQuickFacts facts={cerebralMeningiomaData.quickFacts} />

          {/* Overview Section */}
          <ConditionOverviewSection
            title={cerebralMeningiomaData.overview.title}
            description={cerebralMeningiomaData.overview.description}
            keyPoints={cerebralMeningiomaData.overview.keyPoints}
            imageSrc={cerebralMeningiomaData.overview.imageSrc}
            imageAlt={cerebralMeningiomaData.overview.imageAlt}
            imageCaption={cerebralMeningiomaData.overview.imageCaption}
          />

          {/* Symptom Assessment Tool */}
          <div id="symptom-assessment">
            <MeningiomaSymptomAssessment />
          </div>

          {/* Meninges Anatomy */}
          <MeningiomaAnatomySection
            title={cerebralMeningiomaData.anatomy.title}
            description={cerebralMeningiomaData.anatomy.description}
            meninges={cerebralMeningiomaData.anatomy.meninges}
          />

          {/* Types and Grades */}
          <section className="section-spacing section-background">
            <div className="container">
              <div className="text-centre mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">{cerebralMeningiomaData.types.title}</h2>
                <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
                  {cerebralMeningiomaData.types.description}
                </p>
              </div>
              
              <div className="grid gap-8 md:grid-cols-3">
                {cerebralMeningiomaData.types.classifications.map((type, index) => (
                  <div key={index} className="medical-card p-6">
                    <div className="flex items-centre justify-between mb-3">
                      <h3 className="text-enhanced-heading font-semibold text-xl">{type.grade}</h3>
                      <span className={`text-xs px-2 py-1 rounded ${
                        type.grade.includes('I') ? 'badge-routine' :
                        type.grade.includes('II') ? 'badge-routine' :
                        'badge-emergency'
                      }`}>
                        {type.name}
                      </span>
                    </div>
                    <p className="text-muted-foreground mb-4">{type.description}</p>
                    
                    <div className="space-y-3">
                      <div>
                        <h4 className="font-medium text-sm mb-2">Characteristics:</h4>
                        <ul className="space-y-1">
                          {type.characteristics.slice(0, 3).map((char, idx) => (
                            <li key={idx} className="text-sm flex items-start gap-2">
                              <div className="w-1.5 h-1.5 bg-primary rounded-full mt-1.5 flex-shrink-0" />
                              {char}
                            </li>
                          ))}
                        </ul>
                      </div>
                      
                      <div className="space-y-2 pt-2 border-t border-border">
                        <div className="flex justify-between items-center">
                          <span className="text-enhanced-muted text-xs">Prevalence:</span>
                          <span className="text-xs font-medium">{type.prevalence}</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-enhanced-muted text-xs">Prognosis:</span>
                          <span className="text-xs font-medium">{type.prognosis}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Common Locations */}
          <MeningiomaLocationsSection
            title={cerebralMeningiomaData.locations.title}
            description={cerebralMeningiomaData.locations.description}
            commonSites={cerebralMeningiomaData.locations.commonSites}
          />

          {/* Symptoms Section */}
          <section className="section-spacing section-background">
            <div className="container">
              <div className="text-centre mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">Symptoms and Clinical Presentation</h2>
                <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
                  Meningioma symptoms depend on tumour location, size, and growth rate. Many are discovered incidentally before symptoms develop.
                </p>
              </div>
              
              <div className="grid gap-8 md:grid-cols-3">
                {cerebralMeningiomaData.symptoms.map((category, index) => (
                  <div key={index} className="medical-card p-6">
                    <h3 className="flex items-centre gap-2 text-enhanced-heading font-semibold text-xl mb-4">
                      <category.icon className="h-5 w-5 text-primary" />
                      {category.category}
                    </h3>
                    <div className="content-spacing-sm">
                      {category.symptoms.map((symptom, idx) => (
                        <div key={idx} className="border-l-4 border-primary/30 pl-4">
                          <div className="flex items-centre justify-between mb-1">
                            <h4 className="text-enhanced-strong font-medium">{symptom.name}</h4>
                            <span className={`text-xs px-2 py-1 rounded font-semibold ${
                              symptom.severity === 'severe' ? 'badge-emergency' :
                              symptom.severity === 'moderate' ? 'badge-info' :
                              'badge-routine'
                            }`}>
                              {symptom.severity}
                            </span>
                          </div>
                          <p className="text-sm text-muted-foreground mb-1">{symptom.description}</p>
                          <p className="text-enhanced-muted text-xs">Frequency: {symptom.frequency}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Warning Signs Section */}
          <section className="py-16 bg-muted">
            <div className="container">
              <div className="text-centre mb-12">
                <h2 className="text-enhanced-heading text-3xl font-bold mb-4 text-foreground">{cerebralMeningiomaData.warningSigns.title}</h2>
                <p className="text-lg text-foreground max-w-3xl mx-auto">
                  {cerebralMeningiomaData.warningSigns.description}
                </p>
              </div>
              
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                {cerebralMeningiomaData.warningSigns.emergencySigns.map((sign, index) => (
                  <div key={index} className="bg-background rounded-lg p-6 shadow-md border border-border">
                    <h3 className="font-semibold text-lg mb-2 text-foreground">{sign.sign}</h3>
                    <p className="text-sm text-foreground mb-3">{sign.description}</p>
                    <div className="bg-muted border border-border rounded p-3">
                      <p className="text-sm font-medium text-foreground">{sign.action}</p>
                    </div>
                    <div className="mt-2">
                      <span className={`text-xs px-2 py-1 rounded ${
                        sign.urgency === 'immediate' ? 'bg-muted text-primary-foreground' :
                        sign.urgency === 'urgent' ? 'bg-primary text-primary-foreground' :
                        'bg-primary text-primary-foreground'
                      }`}>
                        {sign.urgency.toUpperCase()}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Diagnosis Section */}
          <section className="section-spacing section-background">
            <div className="container">
              <div className="text-centre mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">{cerebralMeningiomaData.diagnosis.title}</h2>
                <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
                  {cerebralMeningiomaData.diagnosis.description}
                </p>
              </div>
              
              <div className="grid gap-8 lg:grid-cols-3">
                {cerebralMeningiomaData.diagnosis.procedures.map((procedure, index) => (
                  <div key={index} className="medical-card p-6">
                    <h3 className="font-semibold text-xl mb-3">{procedure.name}</h3>
                    <p className="text-muted-foreground mb-4">{procedure.description}</p>
                    
                    <div className="space-y-3">
                      <div className="bg-success-light border border-success/30 rounded p-3">
                        <h4 className="font-medium text-sm text-success mb-1">Accuracy</h4>
                        <p className="text-sm text-success">{procedure.accuracy}</p>
                      </div>
                      
                      <div>
                        <h4 className="font-medium text-sm mb-2">Advantages:</h4>
                        <ul className="space-y-1">
                          {procedure.advantages.slice(0, 3).map((advantage, idx) => (
                            <li key={idx} className="text-sm flex items-start gap-2">
                              <div className="w-1.5 h-1.5 bg-success rounded-full mt-1.5 flex-shrink-0" />
                              {advantage}
                            </li>
                          ))}
                        </ul>
                      </div>
                      
                      <div>
                        <h4 className="font-medium text-sm mb-2">Limitations:</h4>
                        <ul className="space-y-1">
                          {procedure.limitations.slice(0, 2).map((limitation, idx) => (
                            <li key={idx} className="text-sm flex items-start gap-2">
                              <div className="w-1.5 h-1.5 bg-info rounded-full mt-1.5 flex-shrink-0" />
                              {limitation}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Treatment Comparison */}
          <MeningiomaTreatmentComparison
            title={cerebralMeningiomaData.treatmentModalities.title}
            description={cerebralMeningiomaData.treatmentModalities.description}
            treatments={cerebralMeningiomaData.treatmentModalities.treatments}
          />

          {/* Surgical Options */}
          <section className="py-16 bg-info">
            <div className="container">
              <div className="text-centre mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">{cerebralMeningiomaData.surgicalOptions.title}</h2>
                <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
                  {cerebralMeningiomaData.surgicalOptions.description}
                </p>
              </div>

              <div className="grid gap-8 lg:grid-cols-3">
                {cerebralMeningiomaData.surgicalOptions.procedures.map((procedure, index) => (
                  <div key={index} className="medical-card p-6">
                    <h3 className="font-semibold text-xl mb-3">{procedure.name}</h3>
                    <p className="text-muted-foreground mb-4">{procedure.description}</p>

                    <div className="content-spacing-sm">
                      <div className="bg-info-light border border-info/30 rounded p-3">
                        <h4 className="font-medium text-sm text-info mb-1">Technique</h4>
                        <p className="text-sm text-foreground">{procedure.technique}</p>
                      </div>

                      <div>
                        <h4 className="font-medium text-sm mb-2">Advantages:</h4>
                        <ul className="space-y-1">
                          {procedure.advantages.slice(0, 3).map((advantage, idx) => (
                            <li key={idx} className="text-sm flex items-start gap-2">
                              <div className="w-1.5 h-1.5 bg-success rounded-full mt-1.5 flex-shrink-0" />
                              {advantage}
                            </li>
                          ))}
                        </ul>
                      </div>

                      <div>
                        <h4 className="font-medium text-sm mb-2">Risks:</h4>
                        <ul className="space-y-1">
                          {procedure.risks.slice(0, 2).map((risk, idx) => (
                            <li key={idx} className="text-sm flex items-start gap-2">
                              <div className="w-1.5 h-1.5 bg-muted rounded-full mt-1.5 flex-shrink-0" />
                              {risk}
                            </li>
                          ))}
                        </ul>
                      </div>

                      <div className="flex justify-between items-centre pt-2 border-t border-border">
                        <span className="text-enhanced-muted text-xs">Success Rate:</span>
                        <span className="text-xs font-medium">{procedure.successRate}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Radiation Therapy */}
          <section className="section-spacing section-background">
            <div className="container">
              <div className="text-centre mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">{cerebralMeningiomaData.radiationTherapy.title}</h2>
                <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
                  {cerebralMeningiomaData.radiationTherapy.description}
                </p>
              </div>

              <div className="grid gap-8 md:grid-cols-2">
                {cerebralMeningiomaData.radiationTherapy.techniques.map((technique, index) => (
                  <div key={index} className="medical-card p-6">
                    <h3 className="font-semibold text-xl mb-4">{technique.name}</h3>
                    <p className="text-muted-foreground mb-4">{technique.description}</p>

                    <div className="content-spacing-sm">
                      <div>
                        <h4 className="font-medium text-sm mb-2">Indications:</h4>
                        <ul className="space-y-1">
                          {technique.indications.map((indication, idx) => (
                            <li key={idx} className="text-sm flex items-start gap-2">
                              <div className="w-1.5 h-1.5 bg-primary rounded-full mt-1.5 flex-shrink-0" />
                              {indication}
                            </li>
                          ))}
                        </ul>
                      </div>

                      <div>
                        <h4 className="font-medium text-sm mb-2">Advantages:</h4>
                        <ul className="space-y-1">
                          {technique.advantages.slice(0, 3).map((advantage, idx) => (
                            <li key={idx} className="text-sm flex items-start gap-2">
                              <div className="w-1.5 h-1.5 bg-success rounded-full mt-1.5 flex-shrink-0" />
                              {advantage}
                            </li>
                          ))}
                        </ul>
                      </div>

                      <div>
                        <h4 className="font-medium text-sm mb-2">Side Effects:</h4>
                        <ul className="space-y-1">
                          {technique.sideEffects.slice(0, 3).map((effect, idx) => (
                            <li key={idx} className="text-sm flex items-start gap-2">
                              <div className="w-1.5 h-1.5 bg-info rounded-full mt-1.5 flex-shrink-0" />
                              {effect}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Follow-up Care */}
          <section className="section-spacing section-background-alt">
            <div className="container">
              <div className="text-centre mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">{cerebralMeningiomaData.followUpCare.title}</h2>
                <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
                  {cerebralMeningiomaData.followUpCare.description}
                </p>
              </div>

              <div className="grid gap-8 md:grid-cols-3">
                {cerebralMeningiomaData.followUpCare.monitoring.map((timeframe, index) => (
                  <div key={index} className="medical-card p-6">
                    <h3 className="font-semibold text-xl mb-3">{timeframe.timeframe}</h3>
                    <p className="text-sm text-muted-foreground mb-4">{timeframe.purpose}</p>
                    <div className="space-y-2">
                      <h4 className="font-medium text-sm">Required Procedures:</h4>
                      <ul className="space-y-1">
                        {timeframe.procedures.map((procedure, idx) => (
                          <li key={idx} className="text-sm flex items-start gap-2">
                            <div className="w-1.5 h-1.5 bg-primary rounded-full mt-1.5 flex-shrink-0" />
                            {procedure}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Living with Meningioma */}
          <section className="section-spacing section-background">
            <div className="container">
              <div className="text-centre mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">{cerebralMeningiomaData.livingWithMeningioma.title}</h2>
                <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
                  {cerebralMeningiomaData.livingWithMeningioma.description}
                </p>
              </div>

              <div className="grid gap-8 md:grid-cols-3">
                {cerebralMeningiomaData.livingWithMeningioma.sections.map((section, index) => (
                  <div key={index} className="medical-card p-6">
                    <h3 className="font-semibold text-xl mb-4">{section.title}</h3>
                    <div className="space-y-3 mb-4">
                      {section.content.map((paragraph, idx) => (
                        <p key={idx} className="text-enhanced-muted text-sm">{paragraph}</p>
                      ))}
                    </div>
                    <div className="space-y-2">
                      <h4 className="font-medium text-sm">Practical Tips:</h4>
                      <ul className="space-y-1">
                        {section.tips.map((tip, idx) => (
                          <li key={idx} className="text-xs text-muted-foreground flex items-start gap-2">
                            <div className="w-1.5 h-1.5 bg-primary rounded-full mt-1.5 flex-shrink-0" />
                            {tip}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Support Resources */}
          <section className="py-16 bg-info">
            <div className="container">
              <div className="text-centre mb-12">
                <h2 className="text-enhanced-heading text-enhanced-heading text-3xl font-bold mb-4">{cerebralMeningiomaData.supportResources.title}</h2>
                <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
                  {cerebralMeningiomaData.supportResources.description}
                </p>
              </div>

              <div className="grid gap-8 md:grid-cols-3">
                {cerebralMeningiomaData.supportResources.resources.map((resourceCategory, index) => (
                  <div key={index} className="medical-card p-6">
                    <h3 className="font-semibold text-xl mb-4">{resourceCategory.category}</h3>
                    <div className="content-spacing-sm">
                      {resourceCategory.items.map((item, idx) => (
                        <div key={idx} className="border-b border-border pb-3 last:border-b-0">
                          <h4 className="font-medium mb-1">{item.name}</h4>
                          <p className="text-sm text-muted-foreground mb-1">{item.description}</p>
                          {item.contact && (
                            <p className="text-xs text-primary">{item.contact}</p>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>
        </main>
      </StandardPageLayout>
    </>
  );
};

export default CerebralMeningioma;
