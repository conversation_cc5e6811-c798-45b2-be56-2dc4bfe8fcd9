import React from 'react';

interface TherapeuticInterventionsProps {
  title: string;
  subtitle: string;
  description: string;
  externalLink?: {
    url: string;
    text: string;
  };
  interventions: Array<{
    title: string;
    description: string;
    icon: React.ReactNode;
  }>;
}

/**
 * Therapeutic Interventions Component
 * Displays therapeutic intervention options and treatment approaches
 * Preserves all content from the original Mornington location page
 */
const TherapeuticInterventions: React.FC<TherapeuticInterventionsProps> = ({
  title,
  subtitle,
  description,
  externalLink,
  interventions
}) => {
  return (
    <section className="py-16 bg-primary/5">
      <div className="container">
        <div className="text-center max-w-3xl mx-auto mb-12">
          <h2 className="text-enhanced-heading text-3xl font-bold mb-4">{title}</h2>
          <p className="text-muted-foreground">
            {subtitle}
          </p>
        </div>

        <div className="mt-8 max-w-3xl mx-auto mb-12">
          <p className="text-muted-foreground text-center">
            {description.split(externalLink?.text || '')[0]}
            {externalLink && (
              <a
                href={externalLink.url}
                className="text-primary hover:underline"
                target="_blank"
                rel="noopener noreferrer"
              >
                {externalLink.text}
              </a>
            )}
            {description.split(externalLink?.text || '')[1] || ''}
          </p>
        </div>

        <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-8">
          {interventions.map((intervention, index) => (
            <div key={index} className="card p-6 rounded-lg shadow-md medical-card text-centre">
              <div className="flex justify-centre mb-4">
                <div className="h-16 w-16 rounded-full bg-primary/10 flex items-centre justify-centre">
                  {intervention.icon}
                </div>
              </div>
              <h3 className="text-xl font-semibold mb-3 text-primary">{intervention.title}</h3>
              <p className="text-muted-foreground">
                {intervention.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default TherapeuticInterventions;
