import { 
  Sciss<PERSON>, 
  Target, 
  TrendingUp, 
  Clock, 
  Al<PERSON><PERSON>riangle, 
  CheckCircle, 
  Activity, 
  Eye,
  Pill,
  Zap,
  Shield,
  Settings
} from 'lucide-react';
import React, { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

export interface TreatmentOption {
  name: string;
  description: string;
  successRate: number;
  recoveryTime: string;
  invasiveness: 'Low' | 'Medium' | 'High';
  advantages: string[];
  disadvantages: string[];
  suitableFor: string[];
  risks: string[];
}

interface TreatmentComparisonBaseProps {
  treatments: TreatmentOption[];
  title?: string;
  description?: string;
  className?: string;
}

/**
 * Base Treatment Comparison Component
 * Reusable component for comparing different treatment options across medical conditions
 */
export const TreatmentComparisonBase: React.FC<TreatmentComparisonBaseProps> = ({
  treatments,
  title = "Treatment Options Comparison",
  description = "Compare different treatment modalities to understand the best approach for your specific situation.",
  className
}) => {
  const deviceInfo = useDeviceDetection();
  const [selectedTreatment, setSelectedTreatment] = useState<string>(treatments[0]?.name || '');
  const [comparisonMode, setComparisonMode] = useState(false);

  const getTreatmentIcon = (treatmentName: string) => {
    const name = treatmentName.toLowerCase();
    if (name.includes('surgery') || name.includes('surgical')) return Scissors;
    if (name.includes('medication') || name.includes('drug')) return Pill;
    if (name.includes('injection') || name.includes('botox')) return Target;
    if (name.includes('radiation') || name.includes('radiosurgery')) return Zap;
    if (name.includes('observation') || name.includes('monitoring')) return Eye;
    if (name.includes('conservative') || name.includes('therapy')) return Shield;
    if (name.includes('device') || name.includes('implant')) return Settings;
    return Activity;
  };

  const getInvasivenessColor = (invasiveness: string) => {
    switch (invasiveness) {
      case 'Low': return 'bg-green-500';
      case 'Medium': return 'bg-yellow-500';
      case 'High': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const getInvasivenessValue = (invasiveness: string) => {
    switch (invasiveness) {
      case 'Low': return 25;
      case 'Medium': return 60;
      case 'High': return 90;
      default: return 50;
    }
  };

  if (!treatments || treatments.length === 0) {
    return (
      <div className="text-centre py-8">
        <p className="text-muted-foreground">No treatment options available.</p>
      </div>
    );
  }

  return (
    <section className={cn("py-16", className)}>
      <div className="container">
        <div className="text-center mb-12">
          <h2 className="text-enhanced-heading text-3xl font-bold mb-4">{title}</h2>
          <p className="text-enhanced-body max-w-3xl mx-auto mb-8">
            {description}
          </p>

          <div className="flex justify-center gap-4 mb-8">
            <Button
              variant={!comparisonMode ? "default" : "outline"}
              onClick={() => setComparisonMode(false)}
              size={deviceInfo.isMobile ? "sm" : "default"}
            >
              Detailed View
            </Button>
            <Button
              variant={comparisonMode ? "default" : "outline"}
              onClick={() => setComparisonMode(true)}
              size={deviceInfo.isMobile ? "sm" : "default"}
            >
              Compare All
            </Button>
          </div>
        </div>

        {!comparisonMode ? (
          /* Detailed View */
          <Tabs value={selectedTreatment} onValueChange={setSelectedTreatment} className="w-full">
            <TabsList className={cn(
              "grid w-full mb-8",
              deviceInfo.isMobile ? "grid-cols-1 h-auto" : `grid-cols-${Math.min(treatments.length, 3)}`
            )}>
              {treatments.map((treatment) => (
                <TabsTrigger 
                  key={treatment.name} 
                  value={treatment.name}
                  className={cn(
                    "text-centre",
                    deviceInfo.isMobile ? "py-3 text-sm" : "py-4"
                  )}
                >
                  {treatment.name.length > 20 ? 
                    treatment.name.substring(0, 20) + '...' : 
                    treatment.name
                  }
                </TabsTrigger>
              ))}
            </TabsList>

            {treatments.map((treatment) => {
              const Icon = getTreatmentIcon(treatment.name);
              return (
                <TabsContent key={treatment.name} value={treatment.name}>
                  <Card className="medical-card">
                    <CardHeader>
                      <div className="flex items-centre gap-3 mb-4">
                        <div className="p-3 rounded-full bg-primary/10">
                          <Icon className="h-6 w-6 text-primary" />
                        </div>
                        <div>
                          <CardTitle className="text-xl">{treatment.name}</CardTitle>
                          <CardDescription>{treatment.description}</CardDescription>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      {/* Key Metrics */}
                      <div className={cn(
                        "grid gap-4",
                        deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-3"
                      )}>
                        <div className="text-centre p-4 rounded-lg bg-muted/50">
                          <div className="text-2xl font-bold text-primary mb-1">
                            {treatment.successRate}%
                          </div>
                          <div className="text-sm text-muted-foreground">Success Rate</div>
                        </div>
                        <div className="text-centre p-4 rounded-lg bg-muted/50">
                          <div className="text-2xl font-bold text-primary mb-1">
                            {treatment.recoveryTime}
                          </div>
                          <div className="text-sm text-muted-foreground">Recovery Time</div>
                        </div>
                        <div className="text-centre p-4 rounded-lg bg-muted/50">
                          <div className="mb-2">
                            <Progress 
                              value={getInvasivenessValue(treatment.invasiveness)} 
                              className="h-2"
                            />
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {treatment.invasiveness} Invasiveness
                          </div>
                        </div>
                      </div>

                      {/* Advantages & Disadvantages */}
                      <div className={cn(
                        "grid gap-6",
                        deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2"
                      )}>
                        <div>
                          <h4 className="font-semibold text-green-700 dark:text-green-400 mb-3 flex items-centre gap-2">
                            <CheckCircle className="h-4 w-4" />
                            Advantages
                          </h4>
                          <ul className="space-y-2">
                            {treatment.advantages.map((advantage, index) => (
                              <li key={index} className="text-sm text-muted-foreground flex items-start gap-2">
                                <CheckCircle className="h-3 w-3 text-green-500 mt-1 flex-shrink-0" />
                                {advantage}
                              </li>
                            ))}
                          </ul>
                        </div>
                        <div>
                          <h4 className="font-semibold text-red-700 dark:text-red-400 mb-3 flex items-centre gap-2">
                            <AlertTriangle className="h-4 w-4" />
                            Considerations
                          </h4>
                          <ul className="space-y-2">
                            {treatment.disadvantages.map((disadvantage, index) => (
                              <li key={index} className="text-sm text-muted-foreground flex items-start gap-2">
                                <AlertTriangle className="h-3 w-3 text-red-500 mt-1 flex-shrink-0" />
                                {disadvantage}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>

                      {/* Suitable For */}
                      <div>
                        <h4 className="font-semibold mb-3">Suitable For:</h4>
                        <div className="flex flex-wrap gap-2">
                          {treatment.suitableFor.map((criteria, index) => (
                            <Badge key={index} variant="secondary" className="text-xs">
                              {criteria}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
              );
            })}
          </Tabs>
        ) : (
          /* Comparison View */
          <div className="overflow-x-auto">
            <div className={cn(
              "grid gap-6",
              deviceInfo.isMobile ? "grid-cols-1" : `grid-cols-${Math.min(treatments.length, 3)}`
            )}>
              {treatments.map((treatment) => {
                const Icon = getTreatmentIcon(treatment.name);
                return (
                  <Card key={treatment.name} className="medical-card">
                    <CardHeader className="pb-4">
                      <div className="flex items-center gap-3">
                        <div className="p-2 rounded-full bg-primary/10">
                          <Icon className="h-5 w-5 text-primary" />
                        </div>
                        <CardTitle className="text-lg">{treatment.name}</CardTitle>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="text-centre">
                        <div className="text-xl font-bold text-primary">
                          {treatment.successRate}%
                        </div>
                        <div className="text-xs text-muted-foreground">Success Rate</div>
                      </div>
                      
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>Recovery:</span>
                          <span className="font-medium">{treatment.recoveryTime}</span>
                        </div>
                        <div className="space-y-1">
                          <div className="flex justify-between text-sm">
                            <span>Invasiveness:</span>
                            <span className="font-medium">{treatment.invasiveness}</span>
                          </div>
                          <Progress 
                            value={getInvasivenessValue(treatment.invasiveness)} 
                            className="h-1"
                          />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>
        )}
      </div>
    </section>
  );
};

export default TreatmentComparisonBase;
