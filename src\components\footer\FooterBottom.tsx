import React from 'react';
import { Link } from 'react-router-dom';

import { useDeviceDetection } from '@/contexts/DeviceContext';
import { FooterLink } from '@/data/footer/footerData';
import { cn } from '@/lib/utils';

interface FooterBottomProps {
  companyName: string;
  copyrightYear: number;
  copyrightText: string;
  legalLinks: FooterLink[];
}

const FooterBottom: React.FC<FooterBottomProps> = ({
  companyName,
  copyrightYear,
  copyrightText,
  legalLinks
}) => {
  const deviceInfo = useDeviceDetection();

  return (
    <div className={cn(
      "border-t border-border text-center",
      deviceInfo.isMobile
        ? "pt-mobile-lg mt-mobile-lg"
        : "pt-8 mt-8"
    )}>
      <p className={cn(
        "text-enhanced-caption",
        deviceInfo.isMobile ? "mobile-text" : ""
      )}>
        &copy; {copyrightYear} {companyName} | miNEURO Brain and Spine Surgery. {copyrightText}
      </p>
      <div className={cn(
        deviceInfo.isMobile
          ? "mt-mobile-md flex flex-col space-y-mobile-sm"
          : "mt-2 space-x-4"
      )}>
        {legalLinks.map((link, index) => (
          <span key={link.path} className="inline-flex items-center">
            <Link
              to={link.path}
              className={cn(
                "text-enhanced-caption link-enhanced-subtle transition-colors touch-feedback",
                deviceInfo.isMobile
                  ? "mobile-text py-mobile-xs"
                  : "hover:text-primary"
              )}
            >
              {link.name}
            </Link>
            {!deviceInfo.isMobile && index < legalLinks.length - 1 && (
              <span className="text-muted-foreground ml-4">|</span>
            )}
          </span>
        ))}
      </div>
    </div>
  );
};

FooterBottom.displayName = 'FooterBottom';

export default FooterBottom;
