import {
  Zap,
  Users,
  TrendingUp,
  AlertTriangle,
  Search,
  ArrowRight,
  Activity,
  CheckCircle,
  Clock,
  Stethoscope,
  Shield,
  Filter,
  Star,
  Brain,
  Hand,
  Footprints,
  Eye
} from 'lucide-react';
import React, { useState, useEffect } from 'react';
import { Helm<PERSON> } from 'react-helmet-async';
import { Link } from 'react-router-dom';

import PageHeader from '@/components/PageHeader';
import AppointmentCallToActionSection from '@/components/patient-resources/AppointmentCallToActionSection';
import SafeImage from '@/components/SafeImage';
import { SectionHeader, GridLayout } from '@/components/shared/CommonSectionPatterns';
import StandardPageLayout from '@/components/StandardPageLayout';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface PeripheralNerveCondition {
  id: string;
  name: string;
  description: string;
  prevalence: string;
  severity: 'mild' | 'moderate' | 'severe';
  symptoms: string[];
  path: string;
  frequency: number; // 1-7, 1 being most common
  location: 'upper-extremity' | 'lower-extremity' | 'head-neck' | 'general';
  treatmentOptions: string[];
  keyFeatures: string[];
  valueProposition: string;
  estimatedReadTime: string;
  nerveType: string;
}

const peripheralNerveConditions: PeripheralNerveCondition[] = [
  {
    id: 'carpal-tunnel-syndrome',
    name: 'Carpal Tunnel Syndrome',
    description: 'Compression of the median nerve at the wrist, causing numbness, tingling, and weakness in the hand.',
    prevalence: '3-6% of population',
    severity: 'moderate',
    symptoms: ['Hand numbness', 'Tingling fingers', 'Wrist pain', 'Grip weakness'],
    path: '/patient-resources/conditions/carpal-tunnel-syndrome',
    frequency: 1,
    location: 'upper-extremity',
    treatmentOptions: ['Endoscopic Release', 'Open Carpal Tunnel Release', 'Steroid Injections', 'Splinting'],
    keyFeatures: ['Minimally Invasive Surgery', 'Rapid Recovery', 'High Success Rates', 'Nerve Decompression'],
    valueProposition: 'Advanced carpal tunnel surgery with minimally invasive techniques for faster recovery and excellent outcomes.',
    estimatedReadTime: '12-15 min',
    nerveType: 'Median Nerve'
  },
  {
    id: 'ulnar-neuropathy',
    name: 'Ulnar Neuropathy',
    description: 'Compression or damage to the ulnar nerve, commonly at the elbow, causing numbness and weakness.',
    prevalence: '2% of population',
    severity: 'moderate',
    symptoms: ['Pinky numbness', 'Ring finger tingling', 'Hand weakness', 'Elbow pain'],
    path: '/patient-resources/conditions/ulnar-neuropathy',
    frequency: 2,
    location: 'upper-extremity',
    treatmentOptions: ['Ulnar Nerve Transposition', 'Cubital Tunnel Release', 'Conservative Management', 'Nerve Grafting'],
    keyFeatures: ['Nerve Protection', 'Function Restoration', 'Advanced Microsurgery', 'Long-term Solutions'],
    valueProposition: 'Expert ulnar nerve treatment with advanced microsurgical techniques for optimal nerve function restoration.',
    estimatedReadTime: '10-12 min',
    nerveType: 'Ulnar Nerve'
  },
  {
    id: 'tarsal-tunnel-syndrome',
    name: 'Tarsal Tunnel Syndrome',
    description: 'Compression of the posterior tibial nerve at the ankle, causing foot pain and numbness.',
    prevalence: '1% of population',
    severity: 'moderate',
    symptoms: ['Foot numbness', 'Heel pain', 'Arch pain', 'Toe tingling'],
    path: '/patient-resources/conditions/tarsal-tunnel-syndrome',
    frequency: 3,
    location: 'lower-extremity',
    treatmentOptions: ['Tarsal Tunnel Release', 'Steroid Injections', 'Orthotics', 'Physical Therapy'],
    keyFeatures: ['Foot Function Restoration', 'Pain Relief', 'Walking Improvement', 'Nerve Decompression'],
    valueProposition: 'Specialised tarsal tunnel treatment for effective foot pain relief and restored mobility.',
    estimatedReadTime: '8-10 min',
    nerveType: 'Posterior Tibial Nerve'
  },
  {
    id: 'meralgia-paresthetica',
    name: 'Meralgia Paresthetica',
    description: 'Compression of the lateral femoral cutaneous nerve, causing numbness and burning in the outer thigh.',
    prevalence: '0.4% of population',
    severity: 'mild',
    symptoms: ['Thigh numbness', 'Burning sensation', 'Tingling', 'Sensitivity to touch'],
    path: '/patient-resources/conditions/meralgia-paresthetica',
    frequency: 4,
    location: 'lower-extremity',
    treatmentOptions: ['Nerve Blocks', 'Surgical Decompression', 'Weight Management', 'Activity Modification'],
    keyFeatures: ['Symptom Relief', 'Minimally Invasive Options', 'Conservative Management', 'Lifestyle Solutions'],
    valueProposition: 'Effective meralgia paresthetica treatment with both conservative and surgical options for lasting relief.',
    estimatedReadTime: '8-10 min',
    nerveType: 'Lateral Femoral Cutaneous Nerve'
  },
  {
    id: 'peroneal-nerve-palsy',
    name: 'Peroneal Nerve Palsy',
    description: 'Damage to the peroneal nerve causing foot drop and difficulty lifting the foot.',
    prevalence: '1.5 per 100,000',
    severity: 'moderate',
    symptoms: ['Foot drop', 'Ankle weakness', 'Toe numbness', 'Walking difficulty'],
    path: '/patient-resources/conditions/peroneal-nerve-palsy',
    frequency: 5,
    location: 'lower-extremity',
    treatmentOptions: ['Nerve Repair', 'Tendon Transfer', 'Ankle-Foot Orthosis', 'Physical Therapy'],
    keyFeatures: ['Function Restoration', 'Walking Improvement', 'Advanced Reconstruction', 'Mobility Solutions'],
    valueProposition: 'Comprehensive peroneal nerve palsy treatment to restore foot function and improve mobility.',
    estimatedReadTime: '10-12 min',
    nerveType: 'Common Peroneal Nerve'
  },
  {
    id: 'occipital-neuralgia',
    name: 'Occipital Neuralgia',
    description: 'Inflammation or injury to the occipital nerves, causing severe headaches and neck pain.',
    prevalence: '3.2 per 100,000',
    severity: 'severe',
    symptoms: ['Sharp head pain', 'Neck pain', 'Scalp tenderness', 'Light sensitivity'],
    path: '/patient-resources/conditions/occipital-neuralgia',
    frequency: 6,
    location: 'head-neck',
    treatmentOptions: ['Nerve Blocks', 'Radiofrequency Ablation', 'Occipital Nerve Stimulation', 'Surgical Decompression'],
    keyFeatures: ['Pain Relief', 'Headache Management', 'Advanced Interventions', 'Quality of Life Improvement'],
    valueProposition: 'Advanced occipital neuralgia treatment for effective headache relief and improved quality of life.',
    estimatedReadTime: '10-12 min',
    nerveType: 'Greater/Lesser Occipital Nerves'
  },
  {
    id: 'peripheral-nerve-tumors',
    name: 'Peripheral Nerve Tumors',
    description: 'Benign or malignant growths affecting peripheral nerves, requiring specialised surgical management.',
    prevalence: '0.001% of population',
    severity: 'severe',
    symptoms: ['Growing mass', 'Nerve pain', 'Weakness', 'Sensory changes'],
    path: '/patient-resources/conditions/peripheral-nerve-tumors',
    frequency: 7,
    location: 'general',
    treatmentOptions: ['Microsurgical Resection', 'Nerve Grafting', 'Observation', 'Radiation Therapy'],
    keyFeatures: ['Nerve Preservation', 'Tumour Removal', 'Function Protection', 'Advanced Microsurgery'],
    valueProposition: 'Expert peripheral nerve tumour surgery with advanced microsurgical techniques for optimal outcomes.',
    estimatedReadTime: '12-15 min',
    nerveType: 'Various Peripheral Nerves'
  }
];

const PeripheralNerveConditions: React.FC = () => {
  const deviceInfo = useDeviceDetection();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSeverity, setSelectedSeverity] = useState<string>('all');
  const [selectedLocation, setSelectedLocation] = useState<string>('all');

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // Filter conditions based on search, severity, and location
  const filteredConditions = peripheralNerveConditions
    .filter(condition =>
      condition.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      condition.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      condition.symptoms.some(symptom => symptom.toLowerCase().includes(searchTerm.toLowerCase())) ||
      condition.treatmentOptions.some(treatment => treatment.toLowerCase().includes(searchTerm.toLowerCase())) ||
      condition.nerveType.toLowerCase().includes(searchTerm.toLowerCase())
    )
    .filter(condition => selectedSeverity === 'all' || condition.severity === selectedSeverity)
    .filter(condition => selectedLocation === 'all' || condition.location === selectedLocation)
    .sort((a, b) => a.frequency - b.frequency);

  // Location statistics
  const locationStats = {
    'upper-extremity': peripheralNerveConditions.filter(c => c.location === 'upper-extremity').length,
    'lower-extremity': peripheralNerveConditions.filter(c => c.location === 'lower-extremity').length,
    'head-neck': peripheralNerveConditions.filter(c => c.location === 'head-neck').length,
    'general': peripheralNerveConditions.filter(c => c.location === 'general').length
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'severe': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'moderate': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
      case 'mild': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const getLocationColor = (location: string) => {
    switch (location) {
      case 'upper-extremity': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'lower-extremity': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      case 'head-neck': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'general': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const getLocationDisplayName = (location: string) => {
    switch (location) {
      case 'upper-extremity': return 'Upper Extremity';
      case 'lower-extremity': return 'Lower Extremity';
      case 'head-neck': return 'Head & Neck';
      case 'general': return 'General';
      default: return location;
    }
  };

  return (
    <StandardPageLayout showHeader={false}>
      <Helmet>
        <title>Peripheral Nerve Conditions | Expert Nerve Surgery | miNEURO</title>
        <meta
          name="description"
          content="Comprehensive guide to 7 peripheral nerve conditions including carpal tunnel syndrome, ulnar neuropathy, and nerve entrapments. Expert nerve surgery with advanced microsurgical techniques and personalised care pathways."
        />
        <meta name="keywords" content="peripheral nerve conditions, carpal tunnel syndrome, ulnar neuropathy, nerve entrapment, nerve surgery, microsurgery, tarsal tunnel, peroneal nerve palsy" />
        <link rel="canonical" href="https://mineuro.com.au/patient-resources/peripheral-nerve-conditions" />
      </Helmet>

      <PageHeader
        title="Peripheral Nerve Conditions"
        subtitle="Expert nerve surgery for 7 peripheral nerve conditions with advanced microsurgical techniques, comprehensive information, and personalised care pathways"
        backgroundImage="/images/nerve-conditions/peripheral-nerve-anatomy-hero.jpg"
        enableParallax={true}
      />

      <main className="flex-1">
        {/* Overview Section */}
        <section className="py-16 bg-gradient-to-br from-primary/5 to-background">
          <div className="container max-w-7xl">
            <div className="text-centre mb-12">
              <h2 className="text-enhanced-heading text-3xl font-bold mb-4">
                Comprehensive Peripheral Nerve Care
              </h2>
              <p className="text-enhanced-body max-w-4xl mx-auto text-lg">
                Expert nerve surgery for 7 peripheral nerve conditions with advanced microsurgical techniques,
                personalised care pathways, and comprehensive support throughout your journey.
              </p>
            </div>

            {/* Location Overview Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
              <Card className="text-center p-6 border-2 hover:border-primary/30 transition-colors">
                <CardContent className="p-0">
                  <Hand className="h-10 w-10 text-primary mx-auto mb-3" />
                  <h3 className="text-xl font-bold text-enhanced-heading mb-2">Upper Extremity</h3>
                  <p className="text-sm text-muted-foreground mb-2">{locationStats['upper-extremity']} Conditions</p>
                  <p className="text-xs text-muted-foreground">Hand, wrist, and arm nerves</p>
                </CardContent>
              </Card>
              <Card className="text-centre p-6 border-2 hover:border-primary/30 transition-colors">
                <CardContent className="p-0">
                  <Footprints className="h-10 w-10 text-primary mx-auto mb-3" />
                  <h3 className="text-xl font-bold text-enhanced-heading mb-2">Lower Extremity</h3>
                  <p className="text-sm text-muted-foreground mb-2">{locationStats['lower-extremity']} Conditions</p>
                  <p className="text-xs text-muted-foreground">Leg, foot, and ankle nerves</p>
                </CardContent>
              </Card>
              <Card className="text-center p-6 border-2 hover:border-primary/30 transition-colors">
                <CardContent className="p-0">
                  <Brain className="h-10 w-10 text-primary mx-auto mb-3" />
                  <h3 className="text-xl font-bold text-enhanced-heading mb-2">Head & Neck</h3>
                  <p className="text-sm text-muted-foreground mb-2">{locationStats['head-neck']} Conditions</p>
                  <p className="text-xs text-muted-foreground">Cranial and cervical nerves</p>
                </CardContent>
              </Card>
              <Card className="text-centre p-6 border-2 hover:border-primary/30 transition-colors">
                <CardContent className="p-0">
                  <Shield className="h-10 w-10 text-primary mx-auto mb-3" />
                  <h3 className="text-xl font-bold text-enhanced-heading mb-2">General Conditions</h3>
                  <p className="text-sm text-muted-foreground mb-2">{locationStats.general} Conditions</p>
                  <p className="text-xs text-muted-foreground">Multi-location nerve issues</p>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Search and Filter Section */}
        <section className="py-16 bg-muted/30">
          <div className="container max-w-7xl">
            <div className="text-centre mb-8">
              <h2 className="text-enhanced-heading text-2xl font-bold mb-2">
                Find Your Nerve Condition
              </h2>
              <p className="text-enhanced-body text-muted-foreground">
                Search by condition name, symptoms, nerve type, or treatment options
              </p>
            </div>

            <div className={cn(
              "flex gap-4 mb-8",
              deviceInfo.isMobile ? "flex-col" : "flex-row items-centre"
            )}>
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search conditions, symptoms, nerves, or treatments..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>

              <div className="flex gap-2">
                <Select value={selectedLocation} onValueChange={setSelectedLocation}>
                  <SelectTrigger className="w-[160px]">
                    <Filter className="h-4 w-4 mr-2" />
                    <SelectValue placeholder="Location" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Locations</SelectItem>
                    <SelectItem value="upper-extremity">Upper Extremity</SelectItem>
                    <SelectItem value="lower-extremity">Lower Extremity</SelectItem>
                    <SelectItem value="head-neck">Head & Neck</SelectItem>
                    <SelectItem value="general">General</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={selectedSeverity} onValueChange={setSelectedSeverity}>
                  <SelectTrigger className="w-[120px]">
                    <Clock className="h-4 w-4 mr-2" />
                    <SelectValue placeholder="Severity" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Severity</SelectItem>
                    <SelectItem value="mild">Mild</SelectItem>
                    <SelectItem value="moderate">Moderate</SelectItem>
                    <SelectItem value="severe">Severe</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="text-centre text-sm text-muted-foreground">
              Showing {filteredConditions.length} of {peripheralNerveConditions.length} conditions
            </div>
          </div>
        </section>

        {/* Conditions Grid */}
        <section className="py-16">
          <div className="container max-w-7xl">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {filteredConditions.map((condition) => (
                <Card
                  key={condition.id}
                  className="group hover:shadow-xl transition-all duration-300 border-2 hover:border-primary/20 h-full flex flex-col"
                >
                  <CardHeader className="pb-4">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-centre gap-2">
                        <div className="p-2 rounded-lg bg-primary/10 group-hover:bg-primary/20 transition-colors">
                          <Zap className="h-5 w-5 text-primary" />
                        </div>
                        <Badge className={getSeverityColor(condition.severity)}>
                          {condition.severity}
                        </Badge>
                      </div>
                      <div className="flex items-centre gap-1 text-xs text-muted-foreground">
                        <Clock className="h-3 w-3" />
                        {condition.estimatedReadTime}
                      </div>
                    </div>

                    <CardTitle className="text-xl font-bold group-hover:text-primary transition-colors mb-2">
                      {condition.name}
                    </CardTitle>

                    <div className="flex items-centre gap-2 mb-3">
                      <Badge variant="outline" className="text-xs">
                        {getLocationDisplayName(condition.location)}
                      </Badge>
                      <Badge variant="outline" className="text-xs">
                        {condition.nerveType}
                      </Badge>
                    </div>

                    <div className="text-xs text-muted-foreground mb-3">
                      Prevalence: {condition.prevalence}
                    </div>

                    <CardDescription className="text-sm leading-relaxed">
                      {condition.description}
                    </CardDescription>
                  </CardHeader>

                  <CardContent className="flex-1 pb-4">
                    <div className="mb-4">
                      <h4 className="font-semibold mb-2 text-sm flex items-centre gap-1">
                        <Stethoscope className="h-4 w-4 text-primary" />
                        Key Symptoms:
                      </h4>
                      <ul className="text-sm text-muted-foreground space-y-1">
                        {condition.symptoms.slice(0, 3).map((symptom, index) => (
                          <li key={index} className="flex items-centre gap-2">
                            <CheckCircle className="h-3 w-3 text-primary flex-shrink-0" />
                            {symptom}
                          </li>
                        ))}
                        {condition.symptoms.length > 3 && (
                          <li className="text-xs text-muted-foreground italic">
                            +{condition.symptoms.length - 3} more symptoms
                          </li>
                        )}
                      </ul>
                    </div>

                    <div className="mb-4">
                      <h4 className="font-semibold mb-2 text-sm flex items-centre gap-1">
                        <Star className="h-4 w-4 text-primary" />
                        Treatment Options:
                      </h4>
                      <div className="flex flex-wrap gap-1">
                        {condition.treatmentOptions.slice(0, 2).map((treatment, index) => (
                          <Badge key={index} variant="secondary" className="text-xs">
                            {treatment}
                          </Badge>
                        ))}
                        {condition.treatmentOptions.length > 2 && (
                          <Badge variant="outline" className="text-xs">
                            +{condition.treatmentOptions.length - 2} more
                          </Badge>
                        )}
                      </div>
                    </div>

                    <div className="mb-4 p-3 bg-primary/5 rounded-lg">
                      <p className="text-sm text-primary font-medium">
                        {condition.valueProposition}
                      </p>
                    </div>
                  </CardContent>

                  <div className="p-6 pt-0">
                    <Button asChild className="w-full group-hover:bg-primary group-hover:text-primary-foreground transition-colors">
                      <Link to={condition.path} className="flex items-centre justify-centre gap-2">
                        <span>Learn More</span>
                        <ArrowRight className="h-4 w-4" />
                      </Link>
                    </Button>
                  </div>
                </Card>
              ))}
            </div>

            {filteredConditions.length === 0 && (
              <div className="text-center py-16">
                <Zap className="h-20 w-20 text-muted-foreground mx-auto mb-6" />
                <h3 className="text-2xl font-bold mb-4">No nerve conditions found</h3>
                <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                  Try adjusting your search terms or filters to find the information you're looking for.
                </p>
                <div className="flex gap-2 justify-center">
                  <Button
                    onClick={() => {
                      setSearchTerm('');
                      setSelectedSeverity('all');
                      setSelectedLocation('all');
                    }}
                  >
                    Clear All Filters
                  </Button>
                  <Button variant="outline" asChild>
                    <Link to="/patient-resources">
                      Browse All Resources
                    </Link>
                  </Button>
                </div>
              </div>
            )}
          </div>
        </section>

        {/* Comprehensive Peripheral Nerve Anatomy */}
        <section className="py-20 bg-gradient-to-br from-muted/20 to-background">
          <div className="container max-w-7xl">
            <div className="text-centre mb-16">
              <h2 className="text-enhanced-heading text-4xl font-bold mb-6">
                Understanding Your Peripheral Nervous System
              </h2>
              <p className="text-enhanced-body text-xl text-muted-foreground max-w-4xl mx-auto leading-relaxed">
                The peripheral nervous system is a complex network of nerves that connects your brain and spinal cord
                to every part of your body. Understanding this intricate system helps you recognise symptoms,
                appreciate treatment options, and make informed decisions about your care.
              </p>
            </div>

            {/* Main Anatomy Overview with Image */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
              <div className="space-y-6">
                <h3 className="text-enhanced-heading text-2xl font-bold mb-4">
                  The Peripheral Nerve Network
                </h3>
                <p className="text-enhanced-body text-lg leading-relaxed">
                  Your peripheral nervous system consists of 43 pairs of nerves that branch out from your brain and spinal cord.
                  These nerves form an intricate communication network that controls movement, sensation, and vital bodily functions.
                </p>
                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <div className="p-2 rounded-lg bg-primary/10">
                      <Brain className="h-5 w-5 text-primary" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-enhanced-heading">12 Cranial Nerves</h4>
                      <p className="text-muted-foreground text-sm">Control facial movement, vision, hearing, and other head functions</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="p-2 rounded-lg bg-primary/10">
                      <Activity className="h-5 w-5 text-primary" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-enhanced-heading">31 Spinal Nerves</h4>
                      <p className="text-muted-foreground text-sm">Branch from the spinal cord to control body movement and sensation</p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="relative">
                <SafeImage
                  src="https://images.unsplash.com/photo-1559757148-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                  alt="Peripheral nervous system anatomy illustration showing nerve pathways throughout the human body"
                  className="w-full h-[400px] object-cover rounded-xl shadow-2xl"
                  fallbackSrc="/images/nerve-conditions/peripheral-nerve-anatomy.jpg"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-xl" />
              </div>
            </div>

            {/* Nerve Function Types */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
              <Card className="border-2 hover:border-primary/30 transition-colors">
                <CardHeader className="text-centre pb-4">
                  <div className="p-4 rounded-full bg-primary/10 w-fit mx-auto mb-4">
                    <Zap className="h-8 w-8 text-primary" />
                  </div>
                  <CardTitle className="text-xl">Motor Nerves</CardTitle>
                </CardHeader>
                <CardContent className="text-centre">
                  <p className="text-muted-foreground mb-4">
                    Control voluntary muscle movement and strength throughout your body.
                  </p>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-centre gap-2">
                      <CheckCircle className="h-4 w-4 text-primary" />
                      <span>Muscle contraction</span>
                    </div>
                    <div className="flex items-centre gap-2">
                      <CheckCircle className="h-4 w-4 text-primary" />
                      <span>Fine motor control</span>
                    </div>
                    <div className="flex items-centre gap-2">
                      <CheckCircle className="h-4 w-4 text-primary" />
                      <span>Coordination</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="border-2 hover:border-primary/30 transition-colors">
                <CardHeader className="text-centre pb-4">
                  <div className="p-4 rounded-full bg-primary/10 w-fit mx-auto mb-4">
                    <Eye className="h-8 w-8 text-primary" />
                  </div>
                  <CardTitle className="text-xl">Sensory Nerves</CardTitle>
                </CardHeader>
                <CardContent className="text-centre">
                  <p className="text-muted-foreground mb-4">
                    Carry sensation information from your body to your brain.
                  </p>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-centre gap-2">
                      <CheckCircle className="h-4 w-4 text-primary" />
                      <span>Touch and pressure</span>
                    </div>
                    <div className="flex items-centre gap-2">
                      <CheckCircle className="h-4 w-4 text-primary" />
                      <span>Temperature</span>
                    </div>
                    <div className="flex items-centre gap-2">
                      <CheckCircle className="h-4 w-4 text-primary" />
                      <span>Pain and vibration</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="border-2 hover:border-primary/30 transition-colors">
                <CardHeader className="text-center pb-4">
                  <div className="p-4 rounded-full bg-primary/10 w-fit mx-auto mb-4">
                    <Activity className="h-8 w-8 text-primary" />
                  </div>
                  <CardTitle className="text-xl">Autonomic Nerves</CardTitle>
                </CardHeader>
                <CardContent className="text-center">
                  <p className="text-muted-foreground mb-4">
                    Control involuntary functions like heart rate and digestion.
                  </p>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-primary" />
                      <span>Heart rate</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-primary" />
                      <span>Blood pressure</span>
                    </div>
                    <div className="flex items-centre gap-2">
                      <CheckCircle className="h-4 w-4 text-primary" />
                      <span>Digestion</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Common Nerve Locations with Images */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
              <div className="space-y-8">
                <h3 className="text-enhanced-heading text-2xl font-bold">
                  Common Nerve Entrapment Sites
                </h3>
                <div className="space-y-6">
                  <div className="flex items-start gap-4 p-4 bg-white/50 rounded-lg">
                    <div className="p-2 rounded-lg bg-primary/10">
                      <Hand className="h-6 w-6 text-primary" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-enhanced-heading mb-2">Upper Extremity Nerves</h4>
                      <p className="text-muted-foreground text-sm mb-2">
                        Median nerve (carpal tunnel), ulnar nerve (cubital tunnel), and radial nerve compressions
                      </p>
                      <div className="flex flex-wrap gap-1">
                        <Badge variant="outline" className="text-xs">Carpal Tunnel</Badge>
                        <Badge variant="outline" className="text-xs">Cubital Tunnel</Badge>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-start gap-4 p-4 bg-white/50 rounded-lg">
                    <div className="p-2 rounded-lg bg-primary/10">
                      <Footprints className="h-6 w-6 text-primary" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-enhanced-heading mb-2">Lower Extremity Nerves</h4>
                      <p className="text-muted-foreground text-sm mb-2">
                        Peroneal nerve, posterior tibial nerve, and lateral femoral cutaneous nerve
                      </p>
                      <div className="flex flex-wrap gap-1">
                        <Badge variant="outline" className="text-xs">Tarsal Tunnel</Badge>
                        <Badge variant="outline" className="text-xs">Foot Drop</Badge>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="relative">
                <SafeImage
                  src="https://images.pexels.com/photos/5327585/pexels-photo-5327585.jpeg?auto=compress&cs=tinysrgb&w=800"
                  alt="Medical illustration showing common peripheral nerve entrapment sites in hands and feet"
                  className="w-full h-[350px] object-cover rounded-xl shadow-2xl"
                  fallbackSrc="/images/nerve-conditions/nerve-entrapment-sites.jpg"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-xl" />
              </div>
            </div>
          </div>
        </section>



        {/* Appointment Call to Action */}
        <AppointmentCallToActionSection
          title="Expert Peripheral Nerve Care"
          description="Our specialised microsurgical team provides comprehensive diagnosis and treatment for all peripheral nerve conditions. Schedule a consultation to discuss your specific needs and explore advanced treatment options."
          primaryButtonText="Book Consultation"
          primaryButtonLink="/appointments"
          secondaryButtonText="Contact Us"
          secondaryButtonLink="/contact"
        />
      </main>
    </StandardPageLayout>
  );
};

export default PeripheralNerveConditions;
