import React from 'react';

import StandardPageLayout from '../StandardPageLayout';

import { StandardPageLayoutProps } from './types';

import { withStandardLayout, createLayoutVariant } from '@/lib/layout-utils';
import { cn } from '@/lib/utils';

/**
 * Layout Variants
 * Specialised layout components for different page types
 */

/**
 * Specialised layout for landing pages
 */
export function LandingPageLayout(props: Omit<StandardPageLayoutProps, 'pageType'>): React.ReactElement {
  return (
    <StandardPageLayout
      {...props}
      pageType="landing"
      enableParallax={true}
      className={cn('landing-page', props.className)}
    />
  );
}

/**
 * Specialised layout for service pages
 */
export function ServicePageLayout(props: Omit<StandardPageLayoutProps, 'pageType'>): React.ReactElement {
  return (
    <StandardPageLayout
      {...props}
      pageType="service"
      className={cn('service-page', props.className)}
    />
  );
}

/**
 * Specialised layout for location pages
 */
export function LocationPageLayout(props: Omit<StandardPageLayoutProps, 'pageType'>): React.ReactElement {
  return (
    <StandardPageLayout
      {...props}
      pageType="location"
      className={cn('location-page', props.className)}
    />
  );
}

/**
 * Specialised layout for article/content pages
 */
export function ArticlePageLayout(props: Omit<StandardPageLayoutProps, 'pageType'>): React.ReactElement {
  return (
    <StandardPageLayout
      {...props}
      pageType="article"
      showHeader={props.showHeader ?? true}
      className={cn('article-page', props.className)}
      contentClassName={cn('prose-container', props.contentClassName)}
    />
  );
}

// Pre-configured layout variants
export const MinimalLayout = createLayoutVariant('Minimal', StandardPageLayout, {
  showHeader: false,
  className: 'minimal-layout'
});

export const FullWidthLayout = createLayoutVariant('FullWidth', StandardPageLayout, {
  className: 'full-width-layout',
  contentClassName: 'px-0'
});

export const CenteredLayout = createLayoutVariant('Centered', StandardPageLayout, {
  className: 'centered-layout',
  contentClassName: 'max-w-4xl mx-auto px-4'
});

/**
 * Medical Practice Layout - specialised for medical content
 */
export function MedicalPracticeLayout(props: Omit<StandardPageLayoutProps, 'pageType'>): React.ReactElement {
  return (
    <StandardPageLayout
      {...props}
      pageType="service"
      className={cn('medical-practice-layout', props.className)}
      contentClassName={cn('medical-content', props.contentClassName)}
    />
  );
}

/**
 * Patient Resources Layout - optimized for educational content
 */
export function PatientResourcesLayout(props: Omit<StandardPageLayoutProps, 'pageType'>): React.ReactElement {
  return (
    <StandardPageLayout
      {...props}
      pageType="article"
      showHeader={props.showHeader ?? true}
      className={cn('patient-resources-layout', props.className)}
      contentClassName={cn('educational-content max-w-4xl mx-auto', props.contentClassName)}
    />
  );
}

/**
 * Expertise Showcase Layout - for procedure and expertise pages
 */
export function ExpertiseLayout(props: Omit<StandardPageLayoutProps, 'pageType'>): React.ReactElement {
  return (
    <StandardPageLayout
      {...props}
      pageType="service"
      enableParallax={props.enableParallax ?? true}
      className={cn('expertise-layout', props.className)}
      contentClassName={cn('expertise-content', props.contentClassName)}
    />
  );
}

/**
 * Contact/Appointment Layout - for interactive pages
 */
export function InteractiveLayout(props: Omit<StandardPageLayoutProps, 'pageType'>): React.ReactElement {
  return (
    <StandardPageLayout
      {...props}
      pageType="default"
      className={cn('interactive-layout', props.className)}
      contentClassName={cn('interactive-content', props.contentClassName)}
    />
  );
}

// Pre-configured layout variants for common use cases
export const CompactLayout = createLayoutVariant('Compact', StandardPageLayout, {
  className: 'compact-layout',
  contentClassName: 'max-w-2xl mx-auto px-4',
  showHeader: true
});

export const WideLayout = createLayoutVariant('Wide', StandardPageLayout, {
  className: 'wide-layout',
  contentClassName: 'max-w-7xl mx-auto px-4'
});

export const SplitLayout = createLayoutVariant('Split', StandardPageLayout, {
  className: 'split-layout',
  contentClassName: 'grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-6xl mx-auto px-4'
});

// Export the utilities for external use
// eslint-disable-next-line react-refresh/only-export-components
export { withStandardLayout, createLayoutVariant };
