import { 
  <PERSON>, 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  TrendingUp, 
  Clock, 
  Activity,
  CheckCircle,
  XCircle,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  Gauge,
  <PERSON>,
  Brain,
  Target
} from "lucide-react";
import React, { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface TreatmentOption {
  id: string;
  title: string;
  icon: React.ComponentType<any>;
  description: string;
  effectiveness: number;
  timeToImprovement: string;
  duration: string;
  cost: 'Low' | 'Medium' | 'High';
  invasiveness: 'Non-invasive' | 'Minimally invasive' | 'Invasive';
  pros: string[];
  cons: string[];
  scientificEvidence: string;
  successRate: string;
  recommendedFor: string[];
  contraindications: string[];
}

interface PeronealTreatmentComparisonProps {
  className?: string;
}

const treatmentOptions: TreatmentOption[] = [
  {
    id: 'conservative',
    title: 'Conservative Management',
    icon: Shield,
    description: 'Non-invasive approaches including orthotic devices, activity modification, and protective measures',
    effectiveness: 85,
    timeToImprovement: '2-4 weeks',
    duration: 'Ongoing as needed',
    cost: 'Low',
    invasiveness: 'Non-invasive',
    pros: [
      'Immediate functional improvement with AFO',
      'No surgical risks or complications',
      'Can be started immediately after diagnosis',
      'Cost-effective first-line treatment',
      'Allows natural nerve recovery to occur'
    ],
    cons: [
      'Requires ongoing use of orthotic devices',
      'May not address underlying nerve damage',
      'Cosmetic concerns with visible bracing',
      'Potential skin irritation from devices'
    ],
    scientificEvidence: 'Strong evidence shows AFO significantly improves walking safety and efficiency in foot drop',
    successRate: '80-90% functional improvement',
    recommendedFor: [
      'All patients with foot drop regardless of cause',
      'Acute injuries while awaiting nerve recovery',
      'Patients not suitable for surgery',
      'Those preferring non-surgical options'
    ],
    contraindications: [
      'Severe skin conditions preventing brace use',
      'Significant cognitive impairment affecting compliance',
      'Severe lower limb deformities'
    ]
  },
  {
    id: 'rehabilitation',
    title: 'Rehabilitation Therapy',
    icon: Dumbbell,
    description: 'Comprehensive physical therapy focusing on strengthening, gait training, and functional improvement',
    effectiveness: 75,
    timeToImprovement: '4-8 weeks',
    duration: '3-6 months intensive',
    cost: 'Medium',
    invasiveness: 'Non-invasive',
    pros: [
      'Maximises remaining muscle function',
      'Improves overall strength and balance',
      'Teaches compensatory strategies',
      'Enhances quality of life and independence',
      'May accelerate nerve recovery'
    ],
    cons: [
      'Requires significant time commitment',
      'Progress may be slow and gradual',
      'Dependent on patient motivation',
      'May not restore normal function completely'
    ],
    scientificEvidence: 'Moderate evidence for strengthening and gait training in improving functional outcomes',
    successRate: '70-80% functional improvement',
    recommendedFor: [
      'Patients with partial nerve function',
      'Those motivated for active rehabilitation',
      'Individuals with good overall health',
      'Patients seeking to maximise recovery'
    ],
    contraindications: [
      'Complete nerve transection',
      'Severe medical comorbidities',
      'Inability to participate in therapy',
      'Acute inflammatory conditions'
    ]
  },
  {
    id: 'medication',
    title: 'Medication Therapy',
    icon: Pill,
    description: 'Pharmacological management of neuropathic pain and inflammation associated with nerve injury',
    effectiveness: 60,
    timeToImprovement: '1-3 weeks',
    duration: '3-6 months',
    cost: 'Medium',
    invasiveness: 'Non-invasive',
    pros: [
      'Effective for neuropathic pain relief',
      'Can improve sleep and quality of life',
      'Multiple medication options available',
      'Can be combined with other treatments',
      'May reduce inflammation around nerve'
    ],
    cons: [
      'Does not restore motor function',
      'Potential side effects and interactions',
      'May require ongoing use',
      'Tolerance can develop over time',
      'Some medications require monitoring'
    ],
    scientificEvidence: 'Good evidence for gabapentin and pregabalin in neuropathic pain management',
    successRate: '60-75% pain reduction',
    recommendedFor: [
      'Patients with significant neuropathic pain',
      'Those with sleep disturbance from pain',
      'Adjunct to other treatments',
      'Patients unable to undergo procedures'
    ],
    contraindications: [
      'Allergy to specific medications',
      'Significant kidney or liver disease',
      'Drug interactions with current medications',
      'Pregnancy (for certain medications)'
    ]
  },
  {
    id: 'surgery',
    title: 'Surgical Intervention',
    icon: Scissors,
    description: 'Surgical options including nerve decompression, repair, grafting, or tendon transfer procedures',
    effectiveness: 70,
    timeToImprovement: '3-6 months',
    duration: 'Permanent (if successful)',
    cost: 'High',
    invasiveness: 'Invasive',
    pros: [
      'Potential for significant functional restoration',
      'Addresses underlying structural problems',
      'May restore some natural movement',
      'Can improve long-term outcomes',
      'Multiple surgical options available'
    ],
    cons: [
      'Surgical risks and potential complications',
      'Variable success rates depending on injury',
      'Long recovery time required',
      'May not restore complete function',
      'Risk of additional nerve damage'
    ],
    scientificEvidence: 'Mixed evidence with success rates varying based on injury type and timing of surgery',
    successRate: '50-80% depending on procedure',
    recommendedFor: [
      'Complete nerve transection with identifiable ends',
      'Compressive lesions amenable to decompression',
      'Failed conservative treatment in appropriate candidates',
      'Young patients with good surgical risk'
    ],
    contraindications: [
      'Poor surgical candidates',
      'Chronic denervation >18 months',
      'Severe medical comorbidities',
      'Unrealistic patient expectations'
    ]
  }
];

const PeronealTreatmentComparison: React.FC<PeronealTreatmentComparisonProps> = ({ className }) => {
  const deviceInfo = useDeviceDetection();
  const [selectedTreatment, setSelectedTreatment] = useState<string>('conservative');

  const getCostColour = (cost: string) => {
    switch (cost) {
      case 'Low': return 'bg-success-light text-success border border-success/30';
      case 'Medium': return 'bg-info-light text-info border border-info/30';
      case 'High': return 'bg-muted-light text-foreground border border-border/30';
      default: return 'bg-muted text-muted-foreground';
    }
  };

  const getInvasivenessColour = (invasiveness: string) => {
    switch (invasiveness) {
      case 'Non-invasive': return 'bg-success-light text-success border border-success/30';
      case 'Minimally invasive': return 'bg-info-light text-info border border-info/30';
      case 'Invasive': return 'bg-muted-light text-foreground border border-border/30';
      default: return 'bg-muted text-muted-foreground';
    }
  };

  return (
    <section className={cn(
      "section-background border-y border-border/50",
      deviceInfo.isMobile ? "py-16" : "py-24",
      className
    )}>
      <div className="container">
        {/* Section Header */}
        <div className="text-centre mb-20">
          <Badge variant="info" className="mb-6">
            <TrendingUp className="w-4 h-4 mr-2" />
            Treatment Comparison
          </Badge>
          <h2 className={cn(
            "font-bold text-foreground mb-8 leading-tight",
            deviceInfo.isMobile ? "text-2xl" : "text-3xl lg:text-4xl"
          )}>
            Comprehensive Treatment Analysis
          </h2>
          <p className={cn(
            "text-foreground/80 max-w-4xl mx-auto leading-relaxed font-medium",
            deviceInfo.isMobile ? "text-base" : "text-lg"
          )}>
            Evidence-based comparison of all treatment options for peroneal nerve palsy, 
            helping you understand the pros, cons, and scientific evidence for each approach
          </p>
        </div>

        {/* Treatment Overview Cards */}
        <div className={cn(
          "grid gap-6 mb-12",
          deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2 lg:grid-cols-4"
        )}>
          {treatmentOptions.map((treatment) => {
            const IconComponent = treatment.icon;
            return (
              <Card 
                key={treatment.id} 
                className={cn(
                  "medical-card cursor-pointer transition-all duration-300 hover:shadow-lg",
                  selectedTreatment === treatment.id ? "ring-2 ring-primary" : ""
                )}
                onClick={() => setSelectedTreatment(treatment.id)}
              >
                <CardHeader className="text-center">
                  <div className="p-4 rounded-xl bg-primary/10 border border-primary/20 mb-4 mx-auto w-fit">
                    <IconComponent className="w-8 h-8 text-primary" />
                  </div>
                  <CardTitle className="text-enhanced-subheading">{treatment.title}</CardTitle>
                </CardHeader>
                <CardContent className="text-center space-y-3">
                  <div>
                    <p className="text-enhanced-caption mb-1">Effectiveness</p>
                    <Progress value={treatment.effectiveness} className="h-2" />
                    <p className="text-enhanced-caption mt-1">{treatment.effectiveness}%</p>
                  </div>
                  <div className="flex justify-center gap-2">
                    <Badge className={getCostColour(treatment.cost)}>
                      {treatment.cost} Cost
                    </Badge>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Detailed Treatment Information */}
        {(() => {
          const treatment = treatmentOptions.find(t => t.id === selectedTreatment);
          return treatment ? (
            <Card className="medical-card">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="text-enhanced-heading flex items-centre gap-3">
                      <treatment.icon className="w-6 h-6 text-primary" />
                      {treatment.title}
                    </CardTitle>
                    <p className="text-enhanced-body mt-2">{treatment.description}</p>
                  </div>
                  <div className="flex gap-2">
                    <Badge className={getCostColour(treatment.cost)}>
                      {treatment.cost} Cost
                    </Badge>
                    <Badge className={getInvasivenessColour(treatment.invasiveness)}>
                      {treatment.invasiveness}
                    </Badge>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="overview" className="w-full">
                  <TabsList className="grid w-full grid-cols-4">
                    <TabsTrigger value="overview">Overview</TabsTrigger>
                    <TabsTrigger value="evidence">Evidence</TabsTrigger>
                    <TabsTrigger value="selection">Selection</TabsTrigger>
                    <TabsTrigger value="outcomes">Outcomes</TabsTrigger>
                  </TabsList>

                  <TabsContent value="overview" className="space-y-6 mt-6">
                    <div className={cn(
                      "grid gap-6",
                      deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2"
                    )}>
                      <div>
                        <h4 className="text-enhanced-subheading font-semibold mb-3 flex items-centre gap-2">
                          <CheckCircle className="w-4 h-4 text-success" />
                          Advantages
                        </h4>
                        <ul className="space-y-2">
                          {treatment.pros.map((pro, index) => (
                            <li key={index} className="flex items-start gap-2">
                              <CheckCircle className="w-4 h-4 text-success mt-0.5 flex-shrink-0" />
                              <span className="text-enhanced-body text-sm">{pro}</span>
                            </li>
                          ))}
                        </ul>
                      </div>

                      <div>
                        <h4 className="text-enhanced-subheading font-semibold mb-3 flex items-centre gap-2">
                          <XCircle className="w-4 h-4 text-foreground" />
                          Disadvantages
                        </h4>
                        <ul className="space-y-2">
                          {treatment.cons.map((con, index) => (
                            <li key={index} className="flex items-start gap-2">
                              <XCircle className="w-4 h-4 text-foreground mt-0.5 flex-shrink-0" />
                              <span className="text-enhanced-body text-sm">{con}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>

                    <div className={cn(
                      "grid gap-4",
                      deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-3"
                    )}>
                      <div className="bg-info/5 border border-info/20 rounded-lg p-4">
                        <Clock className="w-5 h-5 text-info mb-2" />
                        <h5 className="text-enhanced-caption font-medium">Time to Improvement</h5>
                        <p className="text-enhanced-body text-sm">{treatment.timeToImprovement}</p>
                      </div>
                      <div className="bg-success/5 border border-success/20 rounded-lg p-4">
                        <Activity className="w-5 h-5 text-success mb-2" />
                        <h5 className="text-enhanced-caption font-medium">Treatment Duration</h5>
                        <p className="text-enhanced-body text-sm">{treatment.duration}</p>
                      </div>
                      <div className="bg-primary/5 border border-primary/20 rounded-lg p-4">
                        <Gauge className="w-5 h-5 text-primary mb-2" />
                        <h5 className="text-enhanced-caption font-medium">Success Rate</h5>
                        <p className="text-enhanced-body text-sm">{treatment.successRate}</p>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="evidence" className="space-y-6 mt-6">
                    <div className="bg-info/5 border border-info/20 rounded-lg p-6">
                      <h4 className="text-enhanced-subheading font-semibold mb-3 flex items-centre gap-2">
                        <Award className="w-5 h-5 text-info" />
                        Scientific Evidence
                      </h4>
                      <p className="text-enhanced-body">{treatment.scientificEvidence}</p>
                    </div>
                  </TabsContent>

                  <TabsContent value="selection" className="space-y-6 mt-6">
                    <div className={cn(
                      "grid gap-6",
                      deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2"
                    )}>
                      <div>
                        <h4 className="text-enhanced-subheading font-semibold mb-3 flex items-centre gap-2">
                          <Star className="w-4 h-4 text-success" />
                          Recommended For
                        </h4>
                        <ul className="space-y-2">
                          {treatment.recommendedFor.map((indication, index) => (
                            <li key={index} className="flex items-start gap-2">
                              <Star className="w-4 h-4 text-success mt-0.5 flex-shrink-0" />
                              <span className="text-enhanced-body text-sm">{indication}</span>
                            </li>
                          ))}
                        </ul>
                      </div>

                      <div>
                        <h4 className="text-enhanced-subheading font-semibold mb-3 flex items-centre gap-2">
                          <AlertTriangle className="w-4 h-4 text-foreground" />
                          Contraindications
                        </h4>
                        <ul className="space-y-2">
                          {treatment.contraindications.map((contraindication, index) => (
                            <li key={index} className="flex items-start gap-2">
                              <AlertTriangle className="w-4 h-4 text-foreground mt-0.5 flex-shrink-0" />
                              <span className="text-enhanced-body text-sm">{contraindication}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="outcomes" className="space-y-6 mt-6">
                    <div className="space-y-4">
                      <div>
                        <h4 className="text-enhanced-subheading font-semibold mb-3">Treatment Effectiveness</h4>
                        <div className="space-y-2">
                          <div className="flex justify-between items-centre">
                            <span className="text-enhanced-body text-sm">Overall Effectiveness</span>
                            <span className="text-enhanced-body text-sm font-medium">{treatment.effectiveness}%</span>
                          </div>
                          <Progress value={treatment.effectiveness} className="h-3" />
                        </div>
                      </div>
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          ) : null;
        })()}

        {/* Treatment Decision Framework */}
        <Card className="medical-card mt-12">
          <CardHeader>
            <CardTitle className="text-enhanced-heading flex items-centre gap-3">
              <Brain className="w-5 h-5 text-primary" />
              Treatment Decision Framework
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className={cn(
              "grid gap-6",
              deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-1 md:grid-cols-3"
            )}>
              <div className="text-centre">
                <div className="p-4 rounded-xl bg-success/10 border border-success/20 mb-4">
                  <Shield className="w-8 h-8 text-success mx-auto" />
                </div>
                <h4 className="text-enhanced-subheading font-semibold mb-2">Immediate Function</h4>
                <p className="text-enhanced-body text-sm">Start with AFO for immediate safety and functional improvement</p>
              </div>
              <div className="text-centre">
                <div className="p-4 rounded-xl bg-info/10 border border-info/20 mb-4">
                  <Target className="w-8 h-8 text-info mx-auto" />
                </div>
                <h4 className="text-enhanced-subheading font-semibold mb-2">Maximise Recovery</h4>
                <p className="text-enhanced-body text-sm">Add rehabilitation therapy to optimise remaining function</p>
              </div>
              <div className="text-centre">
                <div className="p-4 rounded-xl bg-muted/50 border border-border/50 mb-4">
                  <Heart className="w-8 h-8 text-foreground mx-auto" />
                </div>
                <h4 className="text-enhanced-subheading font-semibold mb-2">Individual Approach</h4>
                <p className="text-enhanced-body text-sm">Consider patient factors, goals, and injury characteristics</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </section>
  );
};

export default PeronealTreatmentComparison;
