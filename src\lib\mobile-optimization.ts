import { TOUCH_TARGET } from '@/lib/constants';

/**
 * Mobile Optimisation Utilities
 * Comprehensive mobile performance and security enhancements
 */

/**
 * Mobile performance optimisation class
 */
export class MobileOptimiser {
  private static instance: MobileOptimiser;
  private isInitialised: boolean = false;

  private constructor() {
    // Don't initialize in constructor to prevent duplicate initialization
  }

  static getInstance(): MobileOptimiser {
    if (!MobileOptimiser.instance) {
      MobileOptimiser.instance = new MobileOptimiser();
    }
    return MobileOptimiser.instance;
  }
  /**
   * Initialise mobile optimisations
   */
  public initialise(): void {
    if (typeof window === 'undefined' || this.isInitialised) return;

    // Optimise touch events
    this.optimiseTouchEvents();

    // Optimise viewport
    this.optimiseViewport();

    // Optimise scrolling
    this.optimiseScrolling();

    // Optimise images for mobile
    this.optimiseImages();

    // Add mobile-specific security measures
    this.addMobileSecurity();

    this.isInitialised = true;
  }

  /**
   * Optimise touch events for better performance
   */
  private optimiseTouchEvents(): void {
    // Add passive event listeners for better scroll performance
    const passiveEvents = ['touchstart', 'touchmove', 'wheel'];

    passiveEvents.forEach(event => {
      document.addEventListener(event, () => { }, { passive: true });
    });

    // Optimise touch delay
    document.addEventListener('touchstart', () => {
      // Touch start handled for optimization
    }, { passive: true });

    // Prevent zoom on double tap for better UX
    let lastTouchEnd = 0;
    document.addEventListener('touchend', (e: React.FormEvent) => {
      const now = Date.now();
      if (now - lastTouchEnd <= 300) {
        e.preventDefault();
      }
      lastTouchEnd = now;
    }, false);
  }

  /**
   * Optimise viewport settings
   */
  private optimiseViewport(): void {
    // Ensure proper viewport meta tag
    let viewport = document.querySelector('meta[name="viewport"]') as HTMLMetaElement;

    if (!viewport) {
      viewport = document.createElement('meta');
      viewport.name = 'viewport';
      document.head.appendChild(viewport);
    }
    // Set optimal viewport settings for mobile
    viewport.content = 'width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes, viewport-fit=cover';
  }

  /**
   * Optimise scrolling performance
   */
  private optimiseScrolling(): void {
    // Add smooth scrolling behaviour
    document.documentElement.style.scrollBehavior = 'smooth';

    // Optimise scroll performance with CSS
    const style = document.createElement('style');
    style.textContent = `
      * {
        -webkit-overflow-scrolling: touch;
        overscroll-behaviour: contain;
      body {
        -webkit-text-size-adjust: 100%;
        -webkit-tap-highlight-colour: transparent;
      /* Optimise touch targets */
      button, a, input, select, textarea {
        min-height: 44px;
        min-width: 44px;
      /* Optimise animations for mobile */
      @media (prefers-reduced-motion: no-preference) {
        * {
          animation-duration: 0.3s;
          transition-duration: 0.3s;
    `;
    document.head.appendChild(style);
  }

  /**
   * Optimise images for mobile devices
   */
  private optimiseImages(): void {
    // Add intersection observer for lazy loading
    if ('IntersectionObserver' in window) {
      const imageObserver = new IntersectionObserver((entries: unknown) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const img = entry.target as HTMLImageElement;
            if (img.dataset.src) {
              img.src = img.dataset.src;
              img.removeAttribute('data-src');
              imageObserver.unobserve(img);
            }
          }
        });
      }, {
        rootMargin: '50px 0px',
        threshold: 0.01
      });

      // Observe all images with data-src
      document.querySelectorAll('img[data-src]').forEach(img => {
        imageObserver.observe(img);
      });
    }
  }
  /**
   * Add mobile-specific security measures
   */
  private addMobileSecurity(): void {
    // Prevent context menu on long press (optional)
    document.addEventListener('contextmenu', (e: React.FormEvent) => {
      if (this.isMobileDevice()) {
        e.preventDefault();
      }
    });

    // Prevent text selection on UI elements
    const style = document.createElement('style');
    style.textContent = `
      * {
        -webkit-overflow-scrolling: touch;
        overscroll-behaviour: contain;
      body {
        -webkit-text-size-adjust: 100%;
        -webkit-tap-highlight-colour: transparent;
      /* Optimise touch targets */
      button, a, input, select, textarea {
        min-height: ${TOUCH_TARGET.MIN_SIZE}px;
        min-width: ${TOUCH_TARGET.MIN_SIZE}px;
      /* Optimise animations for mobile */
      @media (prefers-reduced-motion: no-preference) {
        * {
          animation-duration: 0.3s;
          transition-duration: 0.3s;
    `;
    document.head.appendChild(style);

    // Add security meta tags for mobile
    this.addMobileSecurityMeta();
  }

  /**
   * Add mobile-specific security meta tags
   */
  private addMobileSecurityMeta(): void {
    const metaTags = [
      { name: 'format-detection', content: 'telephone=no' },
      { name: 'msapplication-tap-highlight', content: 'no' },
      { name: 'apple-mobile-web-app-capable', content: 'yes' },
      { name: 'apple-mobile-web-app-status-bar-style', content: 'default' },
      { name: 'apple-touch-fullscreen', content: 'yes' },
      { name: 'mobile-web-app-capable', content: 'yes' }
    ];

    metaTags.forEach(tag => {
      let meta = document.querySelector(`meta[name="${tag.name}"]`) as HTMLMetaElement;
      if (!meta) {
        meta = document.createElement('meta');
        meta.name = tag.name;
        document.head.appendChild(meta);
      }
      meta.content = tag.content;
    });
  }

  /**
   * Check if device is mobile
   */
  private isMobileDevice(): boolean {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  }

  /**
   * Optimise font loading for mobile
   */
  public optimiseFonts(): void {
    // Add font-display: swap for better performance
    const style = document.createElement('style');
    style.textContent = `
      * {
        -webkit-overflow-scrolling: touch;
        overscroll-behaviour: contain;
      body {
        -webkit-text-size-adjust: 100%;
        -webkit-tap-highlight-colour: transparent;
      /* Optimise touch targets */
      button, a, input, select, textarea {
        min-height: 44px;
        min-width: 44px;
      /* Optimise animations for mobile */
      @media (prefers-reduced-motion: no-preference) {
        * {
          animation-duration: 0.3s;
          transition-duration: 0.3s;
    `;
    document.head.appendChild(style);
  }

  /**
   * Add PWA-specific optimisations
   */
  public optimisePWA(): void {
    // Only register service worker in production to avoid conflicts with Vite dev server
    if ('serviceWorker' in navigator && import.meta.env.PROD) {
      navigator.serviceWorker.register('/sw.js', {
        scope: '/',
        type: 'classic'
      })
        .then((registration) => {
          if (import.meta.env.DEV) {
            console.log('Service Worker registered successfully:', registration.scope);
          }

          // Handle service worker updates
          registration.addEventListener('updatefound', () => {
            const newWorker = registration.installing;
            if (newWorker) {
              newWorker.addEventListener('statechange', () => {
                if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                  // New service worker is available, prompt user to refresh
                  if (confirm('A new version is available. Refresh to update?')) {
                    window.location.reload();
                  }
                }
              });
            }
          });
        })
        .catch((error) => {
          console.error('Service Worker registration failed:', error);
        });
    } else if (import.meta.env.DEV) {
      // In development, unregister any existing service workers to avoid conflicts
      if ('serviceWorker' in navigator) {
        navigator.serviceWorker.getRegistrations().then(registrations => {
          registrations.forEach(registration => {
            registration.unregister();
          });
        });
      }
    }

    // Add app install prompt handling
    // Extend window interface for PWA properties
    interface WindowWithPWA extends Window {
      deferredPrompt?: Event | null;
    }

    const windowWithPWA = window as WindowWithPWA;

    window.addEventListener('beforeinstallprompt', (e: Event) => {
      e.preventDefault();
      // Store the event for later use if needed
      windowWithPWA.deferredPrompt = e;
    });

    // Handle app installed event
    window.addEventListener('appinstalled', () => {
      // PWA was installed - silent handling in production
      windowWithPWA.deferredPrompt = null;
    });
  }

  /**
   * Optimise network requests for mobile
   */
  public optimiseNetwork(): void {
    // Add connection-aware loading
    if ('connection' in navigator) {
      const connection = (navigator as Navigator & { connection: { effectiveType: string } }).connection;

      if (connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g') {
        // Reduce image quality for slow connections
        document.documentElement.classList.add('slow-connection');
      }
    }
  }

  /**
   * Add mobile accessibility enhancements
   */
  public enhanceAccessibility(): void {
    // Ensure proper focus management on mobile
    document.addEventListener('focusin', (e: React.FormEvent) => {
      const target = e.target as HTMLElement;
      if (target && this.isMobileDevice()) {
        target.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
    });

    // Add high contrast mode detection
    if (window.matchMedia('(prefers-contrast: high)').matches) {
      document.documentElement.classList.add('high-contrast');
    }
    // Add reduced motion detection
    if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
      document.documentElement.classList.add('reduced-motion');
    }
  }

  /**
   * Monitor mobile performance
   */
  public monitorPerformance(): void {
    // Monitor memory usage on mobile
    if ('memory' in performance) {
      const memory = (performance as Performance & { memory: { usedJSHeapSize: number; totalJSHeapSize: number } }).memory;

      if (memory.usedJSHeapSize / memory.totalJSHeapSize > 0.8) {
        // High memory usage detected - could trigger cleanup or analytics
      }
    }
    // Monitor battery status
    if ('getBattery' in navigator) {
      (navigator as Navigator & { getBattery: () => Promise<{ level: number; charging: boolean }> }).getBattery().then(battery => {
        if (battery.level < 0.2 && !battery.charging) {
          // Reduce animations and background processes for low battery
          document.documentElement.classList.add('low-battery');
        }
      });
    }
  }
}
/**
 * Initialise mobile optimisations
 */
export function initialiseMobileOptimisations(): void {
  const optimiser = MobileOptimiser.getInstance();
  optimiser.initialise(); // Proper initialization
  optimiser.optimiseFonts();
  optimiser.optimisePWA();
  optimiser.optimiseNetwork();
  optimiser.enhanceAccessibility();
  optimiser.monitorPerformance();
}

/**
 * Mobile-specific utility functions
 */
export const MobileUtils = {
  /**
   * Check if device supports touch
   */
  isTouchDevice(): boolean {
    return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
  },

  /**
   * Get device pixel ratio
   */
  getDevicePixelRatio(): number {
    return window.devicePixelRatio || 1;
  },

  /**
   * Check if device is in landscape mode
   */
  isLandscape(): boolean {
    return window.innerWidth > window.innerHeight;
  },

  /**
   * Get safe area insets for devices with notches
   */
  getSafeAreaInsets(): { top: number; right: number; bottom: number; left: number } {
    const style = getComputedStyle(document.documentElement);
    return {
      top: parseInt(style.getPropertyValue('env(safe-area-inset-top)') || '0'),
      right: parseInt(style.getPropertyValue('env(safe-area-inset-right)') || '0'),
      bottom: parseInt(style.getPropertyValue('env(safe-area-inset-bottom)') || '0'),
      left: parseInt(style.getPropertyValue('env(safe-area-inset-left)') || '0')
    };
  },

  /**
   * Optimise images for mobile screens
   */
  optimiseImageForMobile(img: HTMLImageElement): void {
    const pixelRatio = MobileUtils.getDevicePixelRatio();
    const width = img.clientWidth * pixelRatio;
    const height = img.clientHeight * pixelRatio;

    // Add responsive image attributes
    img.setAttribute('loading', 'lazy');
    img.setAttribute('decoding', 'async');

    // Set optimal sizes
    if (width && height) {
      img.style.maxWidth = '100%';
      img.style.height = 'auto';
    }
  }
};

MobileOptimiser.displayName = 'MobileOptimiser';

export default MobileOptimiser;
