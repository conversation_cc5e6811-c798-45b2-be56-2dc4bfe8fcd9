import React from 'react';

import { cn } from '@/lib/utils';

interface SectionHeaderProps {
  title: string;
  subtitle?: string;
  description?: string;
  alignment?: 'left' | 'center' | 'right';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  titleClassName?: string;
  subtitleClassName?: string;
  descriptionClassName?: string;
}

const SectionHeader: React.FC<SectionHeaderProps> = ({
  title,
  subtitle,
  description,
  alignment = 'center',
  size = 'md',
  className = '',
  titleClassName = '',
  subtitleClassName = '',
  descriptionClassName = ''
}) => {
  const getAlignmentClass = () => {
    switch (alignment) {
      case 'left':
        return 'text-left';
      case 'right':
        return 'text-right';
      default:
        return 'text-center';
    }
  };

  const getTitleSizeClass = () => {
    switch (size) {
      case 'sm':
        return 'text-2xl';
      case 'lg':
        return 'text-4xl';
      case 'xl':
        return 'text-5xl';
      default:
        return 'text-3xl';
    }
  };

  const getSubtitleSizeClass = () => {
    switch (size) {
      case 'sm':
        return 'text-base';
      case 'lg':
        return 'text-xl';
      case 'xl':
        return 'text-2xl';
      default:
        return 'text-lg';
    }
  };

  const getDescriptionSizeClass = () => {
    switch (size) {
      case 'sm':
        return 'text-sm';
      case 'lg':
        return 'text-lg';
      case 'xl':
        return 'text-xl';
      default:
        return 'text-base';
    }
  };

  return (
    <div className={cn(getAlignmentClass(), className)}>
      {subtitle && (
        <p className={cn(
          getSubtitleSizeClass(),
          'text-enhanced-caption text-primary font-semibold mb-3 uppercase tracking-wider',
          subtitleClassName
        )}>
          {subtitle}
        </p>
      )}

      <h2 className={cn(
        getTitleSizeClass(),
        'text-enhanced-heading font-bold mb-6',
        titleClassName
      )}>
        {title}
      </h2>
      
      {description && (
        <p className={cn(
          getDescriptionSizeClass(),
          'text-enhanced-body',
          alignment === 'center' ? 'max-w-3xl mx-auto' : '',
          descriptionClassName
        )}>
          {description}
        </p>
      )}
    </div>
  );
};

export default SectionHeader;
