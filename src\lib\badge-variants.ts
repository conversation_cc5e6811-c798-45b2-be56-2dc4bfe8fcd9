import { cva } from 'class-variance-authority';

/**
 * Badge component variants
 * Separated from component file to support React Fast Refresh
 */
export const badgeVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      variant: {
        default:
          "border-transparent bg-primary text-primary-foreground hover:bg-primary/80 shadow-sm",
        secondary:
          "border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80 shadow-sm",
        muted:
          "border-transparent bg-muted text-foreground hover:bg-muted/80 shadow-sm",
        outline: "text-foreground border-border bg-card hover:bg-accent",
        success:
          "border-transparent bg-success-light text-success border border-success/30 hover:bg-success/80 shadow-sm",
        warning:
          "border-transparent bg-info-light text-info border border-info/30 hover:bg-info/80 shadow-sm",
        info:
          "border-transparent bg-info-light text-info border border-info/30 hover:bg-info/80 shadow-sm",
        emergency:
          "bg-muted-light text-foreground border border-border/70 font-semibold shadow-sm",
        urgent:
          "bg-info-light text-info border border-info/30 font-semibold shadow-sm",
        routine:
          "bg-success-light text-success border border-success/30 font-semibold shadow-sm",
        medical:
          "bg-medical-blue-light text-medical-blue border border-medical-blue/30 font-semibold shadow-sm",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
);
