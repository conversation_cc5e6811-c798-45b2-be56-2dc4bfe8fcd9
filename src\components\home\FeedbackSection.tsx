import React from 'react';
import { Link } from 'react-router-dom';

import { Button } from '@/components/ui/button';
import type { FeedbackSectionProps } from '@/types/homepage';

/**
 * Feedback and Complaints Section Component
 * Displays feedback handling and complaint procedures
 * Preserves all original content and styling from Index.tsx lines 1011-1073
 */
const FeedbackSection: React.FC<FeedbackSectionProps> = ({ sections }) => {
  return (
    <section className="section medical-card">
      <div className="container">
        <div className="text-centre mb-12">
          <span className="text-primary font-medium uppercase tracking-wider text-sm">
            YOUR FEEDBACK MATTERS
          </span>
          <h2 className="text-enhanced-heading text-3xl md:text-4xl font-bold mt-2 mb-6 text-foreground">Feedback and Complaints</h2>
          <p className="text-foreground/80 max-w-3xl mx-auto leading-relaxed">
            We value your feedback and are committed to continuous improvement. Your input helps us provide the highest quality of care.
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-8 mb-8">
          {sections.map((section) => (
            <FeedbackCard key={section.id} section={section} />
          ))}
        </div>

        <div className="bg-primary/5 border border-primary/10 p-8 rounded-lg text-center">
          <h3 className="text-xl font-semibold mb-4 text-foreground">How to Provide Feedback</h3>
          <p className="text-foreground/80 mb-6 leading-relaxed max-w-2xl mx-auto">
            You can provide feedback through multiple channels. We encourage open communication and are committed to addressing all feedback constructively.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild>
              <Link to="/contact">
                Contact Our Practice
              </Link>
            </Button>
            <Button variant="outline" asChild>
              <a href="tel:+61-3-9xxx-xxxx">
                Call Us Directly
              </a>
            </Button>
          </div>
          <p className="text-sm text-foreground/70 mt-6 max-w-2xl mx-auto leading-relaxed">
            For formal complaints, external review options include AHPRA (Australian Health Practitioner Regulation Agency) and the Health Complaints Commissioner.
          </p>
        </div>
      </div>
    </section>
  );
};

/**
 * Individual Feedback Card Component
 */
interface FeedbackCardProps {
  section: {
    id: string;
    title: string;
    description: string;
    items: string[];
    bgColor: string;
    borderColor?: string;
  };
}

const FeedbackCard: React.FC<FeedbackCardProps> = ({ section }) => {
  const cardClasses = `${section.bgColor} p-6 rounded-lg border ${section.borderColor || 'border-border'}`;

  return (
    <div className={cardClasses}>
      <h3 className="text-xl font-semibold mb-4 text-foreground">{section.title}</h3>
      <p className="text-foreground/80 mb-4 leading-relaxed">
        {section.description}
      </p>
      <ul className="space-y-3 text-foreground/75">
        {section.items.map((item, index) => (
          <li key={index} className="flex items-start gap-2">
            <span className="text-primary mt-1 font-medium">•</span>
            <span>{item}</span>
          </li>
        ))}
      </ul>
    </div>
  );
};

FeedbackSection.displayName = 'FeedbackSection';

export default FeedbackSection;
