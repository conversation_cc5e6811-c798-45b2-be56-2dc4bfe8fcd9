import { Clock } from 'lucide-react';
import React from 'react';
import { Link } from 'react-router-dom';

import { Button } from '@/components/ui/button';

interface AppointmentCallToActionSectionProps {
  title: string;
  description: string;
  primaryButtonText: string;
  primaryButtonLink: string;
  secondaryButtonText: string;
  secondaryButtonLink: string;
}

const AppointmentCallToActionSection: React.FC<AppointmentCallToActionSectionProps> = ({
  title,
  description,
  primaryButtonText,
  primaryButtonLink,
  secondaryButtonText,
  secondaryButtonLink
}) => {
  return (
    <section className="py-16">
      <div className="container max-w-7xl">
        <div className="max-w-3xl mx-auto text-center">
          <div className="mb-6">
            <div className="inline-flex p-3 rounded-full bg-primary/10 mb-4">
              <Clock className="h-8 w-8 text-primary" />
            </div>
            <h2 className="text-enhanced-heading text-3xl font-bold mb-4 text-foreground">{title}</h2>
            <p className="text-muted-foreground leading-relaxed">{description}</p>
          </div>
          
          <div className="flex flex-col sm:flex-row justify-centre gap-4">
            <Button asChild size="lg" className="min-w-[200px]">
              <Link to={primaryButtonLink}>{primaryButtonText}</Link>
            </Button>
            <Button asChild variant="outline" size="lg" className="min-w-[200px]">
              <Link to={secondaryButtonLink}>{secondaryButtonText}</Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

AppointmentCallToActionSection.displayName = 'AppointmentCallToActionSection';

export default AppointmentCallToActionSection;
