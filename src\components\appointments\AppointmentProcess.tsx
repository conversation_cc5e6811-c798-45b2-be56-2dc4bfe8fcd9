import { LucideIcon } from 'lucide-react';
import React from 'react';

import { Card, CardContent } from '@/components/ui/card';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface ProcessStep {
  number: number;
  icon: LucideIcon;
  title: string;
  description: string;
}

interface AppointmentProcessProps {
  title: string;
  subtitle: string;
  steps: ProcessStep[];
}

const AppointmentProcess: React.FC<AppointmentProcessProps> = ({
  title,
  subtitle,
  steps
}) => {
  const deviceInfo = useDeviceDetection();

  return (
    <section className={cn(
      "bg-primary/5",
      deviceInfo.isMobile ? "mobile-section" : "py-16"
    )}>
      <div className={deviceInfo.isMobile ? "mobile-container" : "container"}>
        <h2 className={cn(
          "font-bold text-center mb-mobile-lg",
          deviceInfo.isMobile
            ? "mobile-3xl"
            : "text-3xl mb-8"
        )}>
          {title}
        </h2>
        <p className={cn(
          "text-center text-muted-foreground mx-auto mb-mobile-xl",
          deviceInfo.isMobile
            ? "mobile-text max-w-full"
            : "mb-12 max-w-3xl"
        )}>
          {subtitle}
        </p>

        <div className={cn(
          "grid gap-8",
          deviceInfo.isMobile
            ? "grid-cols-1 gap-6"
            : "grid-cols-1 md:grid-cols-2 lg:grid-cols-4"
        )}>
          {steps.map((step) => {
            return (
              <Card key={step.number} className={cn(
                "transition-all duration-300 hover:shadow-lg hover:border-primary/20",
                deviceInfo.isMobile ? "p-mobile-lg" : "p-6"
              )}>
                <CardContent className="p-0">
                  <div className={cn(
                    "flex items-centre justify-centre rounded-full bg-primary/10 text-primary font-bold mb-4",
                    deviceInfo.isMobile ? "w-12 h-12 text-lg" : "w-16 h-16 text-2xl"
                  )}>
                    {step.number}
                  </div>
                  <h3 className={cn(
                    "font-semibold text-primary mb-3",
                    deviceInfo.isMobile
                      ? "mobile-subheading"
                      : "text-xl"
                  )}>
                    {step.title}
                  </h3>
                  <p className={cn(
                    "text-muted-foreground leading-relaxed",
                    deviceInfo.isMobile ? "mobile-text" : ""
                  )}>
                    {step.description}
                  </p>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default AppointmentProcess;
