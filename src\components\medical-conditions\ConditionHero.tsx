import { Target, Calendar, ChevronRight } from 'lucide-react';
import React from 'react';
import { Link } from 'react-router-dom';

import SafeImage from '@/components/SafeImage';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { useLanguage } from '@/contexts/LanguageContext';
import { cn } from '@/lib/utils';

/**
 * Condition Hero Component
 * Reusable hero section for medical condition pages
 */

// Breadcrumb item interface
interface BreadcrumbItem {
  label: string;
  href: string;
}

// Updated props interface to match test expectations
interface ConditionHeroProps {
  data: {
    title: string;
    subtitle?: string;
    backgroundImage?: string;
    breadcrumbs?: BreadcrumbItem[];
  };
}

const ConditionHero: React.FC<ConditionHeroProps> = React.memo(({
  data
}) => {
  // Always call hooks at the top level - use error boundaries for error handling
  const deviceInfo = useDeviceDetection();
  const { t } = useLanguage();

  const { title, subtitle, backgroundImage, breadcrumbs } = data;

  return (
    <div>
      {/* Breadcrumb Navigation */}
      {breadcrumbs && breadcrumbs.length > 0 && (
        <nav aria-label="Breadcrumb" className="section-background-muted section-spacing ">
          <div className={cn("container", deviceInfo.isMobile ? "px-4" : "")}>
            <ol className="flex items-centre space-x-2 text-sm">
              {breadcrumbs.map((crumb, index) => (
                <li key={index} className="flex items-centre">
                  {index > 0 && <ChevronRight className="h-4 w-4 mx-2 text-muted-foreground" />}
                  <Link
                    to={crumb.href}
                    className="text-muted-foreground hover:text-foreground transition-colors"
                  >
                    {crumb.label}
                  </Link>
                </li>
              ))}
            </ol>
          </div>
        </nav>
      )}

      {/* Hero Section */}
      <header
        className={cn(
          "relative bg-gradient-to-r from-primary/10 to-white dark:from-primary/20 dark:to-background",
          deviceInfo.isMobile ? "py-12 mobile-optimized" : "py-20",
          deviceInfo.isTablet ? "tablet-optimized" : "",
          deviceInfo.isDesktop ? "desktop-optimized" : ""
        )}
        data-testid="condition-hero"
        aria-label={`${title} hero section`}
        style={backgroundImage ? { backgroundImage: `url(${backgroundImage})` } : undefined}
      >
        {backgroundImage && (
          <div className="absolute inset-0 overflow-hidden opacity-10">
            <SafeImage
              src={backgroundImage}
              alt={`${title} background`}
              className="w-full h-full object-cover"
              fallbackSrc="https://images.unsplash.com/photo-1559757148-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80"
            />
          </div>
        )}
        <div className={cn("container", deviceInfo.isMobile ? "px-4" : "")}>
          <div className="text-center max-w-4xl mx-auto">
            <Badge variant="secondary" className="mb-4">
              {t.common?.spineConditionsLibrary || 'Spine Conditions Library'}
            </Badge>
            <h1 className={cn(
              "font-bold mb-6",
              deviceInfo.isMobile ? "text-3xl" : "text-4xl md:text-5xl"
            )}>
              {title}
            </h1>
            {subtitle && (
              <p className={cn(
                "text-muted-foreground mb-8",
                deviceInfo.isMobile ? "text-base" : "text-lg"
              )}>
                {subtitle}
              </p>
            )}
            <div className={cn("flex", deviceInfo.isMobile ? "flex-col items-center" : "flex-row")}>
              <Button asChild size={deviceInfo.isMobile ? "default" : "lg"}>
                <Link to="/#assessment">
                  <Target className="mr-2 h-4 w-4" />
                  {t.common?.takeAssessment || 'Take Assessment'}
                </Link>
              </Button>
              <Button asChild variant="outline" size={deviceInfo.isMobile ? "default" : "lg"}>
                <Link to="/contact">
                  <Calendar className="mr-2 h-4 w-4" />
                  {t.common?.bookConsultation || 'Book Consultation'}
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </header>
      {/* Quick Facts Section - Placeholder for future implementation */}
      <div className={cn(
        "bg-muted/30",
        deviceInfo.isMobile ? "py-8" : "py-12"
      )}>
        <div className={cn("container", deviceInfo.isMobile ? "px-4" : "")}>
          <div className={cn("grid", deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2 md:grid-cols-4")}>
            {/* Quick facts can be added here in the future */}
          </div>
        </div>
      </div>
    </div>
  );
});

ConditionHero.displayName = 'ConditionHero';

export default ConditionHero;
