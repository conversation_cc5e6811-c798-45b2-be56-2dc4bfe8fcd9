import { <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>dingUp, ArrowRight, ArrowLeft, Activity, Zap } from 'lucide-react';
import React, { useState, useId } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface AssessmentQuestion {
  id: string;
  question: string;
  description?: string;
  options: Array<{
    value: string;
    label: string;
    score: number;
  }>;
}

interface AssessmentResult {
  totalScore: number;
  likelihood: 'low' | 'moderate' | 'high';
  recommendation: string;
  urgency: 'routine' | 'urgent' | 'immediate';
  nextSteps: string[];
  symptoms: string[];
  riskFactors: string[];
}

const assessmentQuestions: AssessmentQuestion[] = [
  {
    id: 'hand-function',
    question: 'Do you have difficulty with fine hand movements like buttoning clothes or writing?',
    description: 'Hand clumsiness is often the earliest sign of cervical myelopathy',
    options: [
      { value: 'severe-difficulty', label: 'Yes, severe difficulty with most fine motor tasks', score: 6 },
      { value: 'moderate-difficulty', label: 'Yes, noticeable difficulty with some tasks', score: 4 },
      { value: 'mild-difficulty', label: 'Yes, mild difficulty occasionally', score: 2 },
      { value: 'no-difficulty', label: 'No difficulty with hand movements', score: 0 }
    ]
  },
  {
    id: 'gait-balance',
    question: 'Do you have problems with walking, balance, or frequent falls?',
    description: 'Gait disturbance is a common and serious symptom of cervical myelopathy',
    options: [
      { value: 'frequent-falls', label: 'Yes, frequent falls and significant walking problems', score: 6 },
      { value: 'unsteady-gait', label: 'Yes, unsteady walking and balance problems', score: 4 },
      { value: 'mild-unsteadiness', label: 'Yes, mild unsteadiness occasionally', score: 2 },
      { value: 'no-problems', label: 'No walking or balance problems', score: 0 }
    ]
  },
  {
    id: 'numbness-tingling',
    question: 'Do you experience numbness or tingling in your hands or feet?',
    description: 'Sensory symptoms often accompany motor problems in cervical myelopathy',
    options: [
      { value: 'severe-numbness', label: 'Yes, severe numbness affecting daily activities', score: 4 },
      { value: 'moderate-numbness', label: 'Yes, noticeable numbness and tingling', score: 3 },
      { value: 'mild-tingling', label: 'Yes, mild tingling occasionally', score: 1 },
      { value: 'no-numbness', label: 'No numbness or tingling', score: 0 }
    ]
  },
  {
    id: 'weakness',
    question: 'Do you have weakness in your arms or legs?',
    description: 'Progressive weakness is a key feature of cervical myelopathy',
    options: [
      { value: 'severe-weakness', label: 'Yes, significant weakness affecting function', score: 5 },
      { value: 'moderate-weakness', label: 'Yes, noticeable weakness in arms or legs', score: 3 },
      { value: 'mild-weakness', label: 'Yes, mild weakness occasionally', score: 1 },
      { value: 'no-weakness', label: 'No weakness', score: 0 }
    ]
  },
  {
    id: 'neck-pain',
    question: 'Do you have neck pain or stiffness?',
    description: 'Neck symptoms may accompany cervical myelopathy',
    options: [
      { value: 'severe-pain', label: 'Yes, severe neck pain limiting activities', score: 3 },
      { value: 'moderate-pain', label: 'Yes, moderate neck pain and stiffness', score: 2 },
      { value: 'mild-pain', label: 'Yes, mild neck discomfort', score: 1 },
      { value: 'no-pain', label: 'No neck pain or stiffness', score: 0 }
    ]
  },
  {
    id: 'progression',
    question: 'Have your symptoms been getting worse over time?',
    description: 'Progressive worsening is characteristic of cervical myelopathy',
    options: [
      { value: 'rapidly-worsening', label: 'Yes, rapidly getting worse over weeks', score: 5 },
      { value: 'gradually-worsening', label: 'Yes, gradually getting worse over months', score: 3 },
      { value: 'stable-symptoms', label: 'Symptoms are stable', score: 1 },
      { value: 'no-progression', label: 'No progression or symptoms improving', score: 0 }
    ]
  },
  {
    id: 'lhermittes-sign',
    question: 'Do you get electric shock sensations down your spine when you bend your neck forward?',
    description: 'Lhermitte\'s sign can indicate spinal cord involvement',
    options: [
      { value: 'frequent-shocks', label: 'Yes, frequent electric shock sensations', score: 3 },
      { value: 'occasional-shocks', label: 'Yes, occasional shock sensations', score: 2 },
      { value: 'rare-shocks', label: 'Yes, rarely', score: 1 },
      { value: 'no-shocks', label: 'No electric shock sensations', score: 0 }
    ]
  }
];

export function MyelopathySymptomAssessment() {
  const deviceInfo = useDeviceDetection();
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [answers, setAnswers] = useState<Record<string, string>>({});
  const [showResults, setShowResults] = useState(false);
  const assessmentId = useId();

  const progress = ((currentQuestion + 1) / assessmentQuestions.length) * 100;
  const isLastQuestion = currentQuestion === assessmentQuestions.length - 1;
  const canProceed = answers[assessmentQuestions[currentQuestion]?.id];

  const handleAnswer = (value: string) => {
    setAnswers(prev => ({
      ...prev,
      [assessmentQuestions[currentQuestion].id]: value
    }));
  };

  const handleNext = () => {
    if (isLastQuestion) {
      setShowResults(true);
    } else {
      setCurrentQuestion(prev => prev + 1);
    }
  };

  const handlePrevious = () => {
    if (currentQuestion > 0) {
      setCurrentQuestion(prev => prev - 1);
    }
  };

  const calculateResults = (): AssessmentResult => {
    const totalScore = Object.entries(answers).reduce((total, [questionId, answer]) => {
      const question = assessmentQuestions.find(q => q.id === questionId);
      const option = question?.options.find(o => o.value === answer);
      return total + (option?.score || 0);
    }, 0);

    let likelihood: 'low' | 'moderate' | 'high';
    let recommendation: string;
    let urgency: 'routine' | 'urgent' | 'immediate';
    let nextSteps: string[];
    const symptoms: string[] = [];
    const riskFactors: string[] = [];

    // Identify specific symptoms
    if (answers['hand-function'] && !answers['hand-function'].includes('no-difficulty')) {
      symptoms.push('Hand dysfunction');
    }
    if (answers['gait-balance'] && !answers['gait-balance'].includes('no-problems')) {
      symptoms.push('Gait/balance problems');
    }
    if (answers['numbness-tingling'] && !answers['numbness-tingling'].includes('no-numbness')) {
      symptoms.push('Sensory symptoms');
    }
    if (answers['weakness'] && !answers['weakness'].includes('no-weakness')) {
      symptoms.push('Muscle weakness');
    }
    if (answers['neck-pain'] && !answers['neck-pain'].includes('no-pain')) {
      symptoms.push('Neck pain');
    }
    if (answers['lhermittes-sign'] && !answers['lhermittes-sign'].includes('no-shocks')) {
      symptoms.push('Lhermitte\'s sign');
    }

    // Identify risk factors
    if (answers['progression'] && answers['progression'].includes('worsening')) {
      riskFactors.push('Progressive symptoms');
    }
    if (answers['hand-function'] && answers['hand-function'].includes('severe')) {
      riskFactors.push('Severe hand dysfunction');
    }
    if (answers['gait-balance'] && answers['gait-balance'].includes('falls')) {
      riskFactors.push('Fall risk');
    }

    // Adjust scoring for high-risk combinations
    let adjustedScore = totalScore;
    if (answers['hand-function']?.includes('severe') && answers['gait-balance']?.includes('falls')) {
      adjustedScore += 3; // Combined motor symptoms are very concerning
    }
    if (answers['progression']?.includes('rapidly')) {
      adjustedScore += 2; // Rapid progression is very concerning
    }

    if (adjustedScore >= 20) {
      likelihood = 'high';
      urgency = 'immediate';
      recommendation = 'Symptoms are highly suggestive of cervical myelopathy and require immediate medical evaluation. The combination and severity of symptoms warrant urgent neurological assessment and spinal imaging.';
      nextSteps = [
        'Go to emergency department or contact neurosurgeon immediately',
        'Request urgent MRI cervical spine',
        'Avoid activities that could worsen neck injury',
        'Do not delay - cervical myelopathy can cause permanent damage'
      ];
    } else if (adjustedScore >= 12) {
      likelihood = 'moderate';
      urgency = 'urgent';
      recommendation = 'Symptoms suggest possible cervical myelopathy and require prompt medical evaluation. Several concerning features are present that need urgent assessment.';
      nextSteps = [
        'Contact GP or neurologist within 24-48 hours',
        'Request urgent referral to neurosurgery',
        'Arrange MRI cervical spine as soon as possible',
        'Monitor symptoms closely for any worsening'
      ];
    } else if (adjustedScore >= 6) {
      likelihood = 'low';
      urgency = 'urgent';
      recommendation = 'Some symptoms are present that could be related to cervical myelopathy. Medical evaluation is recommended to rule out this condition.';
      nextSteps = [
        'Schedule appointment with GP within 1-2 weeks',
        'Discuss symptoms and request neurological assessment',
        'Consider MRI if symptoms persist or worsen',
        'Monitor for any new or worsening symptoms'
      ];
    } else {
      likelihood = 'low';
      urgency = 'routine';
      recommendation = 'Current symptoms are minimal and cervical myelopathy is unlikely. Continue routine health monitoring and be aware of warning signs.';
      nextSteps = [
        'Continue routine health check-ups',
        'Be aware of cervical myelopathy warning signs',
        'Seek medical attention if new symptoms develop',
        'Maintain good neck posture and ergonomics'
      ];
    }

    return { totalScore: adjustedScore, likelihood, recommendation, urgency, nextSteps, symptoms, riskFactors };
  };

  const results = showResults ? calculateResults() : null;

  const getLikelihoodColor = (likelihood: string) => {
    switch (likelihood) {
      case 'high': return 'bg-muted-light text-foreground border-border/70';
      case 'moderate': return 'bg-info-light text-info border-info/30';
      default: return 'bg-success-light text-success border-success/30';
    }
  };

  const getUrgencyIcon = (urgency: string) => {
    switch (urgency) {
      case 'immediate': return <AlertTriangle className="h-5 w-5 text-foreground" />;
      case 'urgent': return <Brain className="h-5 w-5 text-info" />;
      default: return <CheckCircle className="h-5 w-5 text-success" />;
    }
  };

  if (showResults && results) {
    return (
      <section className={cn(
        "py-20 bg-gradient-to-br from-background via-background/95 to-muted/30",
        deviceInfo.isMobile ? "px-4" : ""
      )}>
        <div className="container max-w-4xl">
          <Card className={cn(
            "border-2 shadow-2xl backdrop-blur-sm bg-card/90",
            getLikelihoodColor(results.likelihood)
          )}>
            <CardHeader className="text-center pb-10">
              <div className="flex items-center justify-center gap-4 mb-8">
                <div className="p-4 rounded-xl bg-background/90 dark:bg-muted/90 shadow-xl border border-border/50">
                  {getUrgencyIcon(results.urgency)}
                </div>
                <CardTitle className={cn(
                  "font-bold text-foreground leading-tight",
                  deviceInfo.isMobile ? "text-xl" : "text-2xl lg:text-3xl"
                )}>
                  Cervical Myelopathy Assessment Results
                </CardTitle>
              </div>
              <CardDescription className="text-base text-foreground/80 max-w-2xl mx-auto leading-relaxed font-medium">
                Based on your responses, here's your personalised assessment for cervical myelopathy symptoms
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-10 pb-10">
              {/* Score and Likelihood */}
              <div className="text-centre p-10 bg-background/90 dark:bg-muted/90 backdrop-blur-sm rounded-xl border border-border/50 shadow-xl">
                <div className="flex items-centre justify-centre gap-8 mb-8">
                  <div className="text-centre">
                    <div className="text-5xl font-bold text-primary mb-3">{results.totalScore}</div>
                    <div className="text-sm text-muted-foreground font-semibold">Assessment Score</div>
                  </div>
                  <div className="h-16 w-px bg-border/50"></div>
                  <div className="text-centre">
                    <Badge className={cn("text-sm font-bold px-6 py-3 shadow-lg", getLikelihoodColor(results.likelihood))}>
                      {results.likelihood.toUpperCase()} LIKELIHOOD
                    </Badge>
                    <div className="text-xs text-muted-foreground mt-3 font-medium">Risk Level</div>
                  </div>
                </div>
              </div>

              {/* Identified Symptoms */}
              {results.symptoms.length > 0 && (
                <div className="medical-card-inner rounded-xl p-8 border border-border/50">
                  <h3 className="text-enhanced-heading font-bold mb-6 flex items-centre gap-3">
                    <Zap className="h-6 w-6 text-primary" />
                    Identified Symptoms
                  </h3>
                  <div className="flex flex-wrap gap-3">
                    {results.symptoms.map((symptom, index) => (
                      <Badge
                        key={index}
                        className="badge-info px-4 py-2 text-sm font-semibold"
                      >
                        {symptom}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {/* Risk Factors */}
              {results.riskFactors.length > 0 && (
                <div className="bg-background/90 dark:bg-muted/90 backdrop-blur-sm rounded-xl p-8 border border-border/50 shadow-lg">
                  <h3 className="font-bold mb-6 flex items-centre gap-3 text-foreground">
                    <AlertTriangle className="h-6 w-6 text-info" />
                    Risk Factors
                  </h3>
                  <div className="flex flex-wrap gap-3">
                    {results.riskFactors.map((factor, index) => (
                      <Badge
                        key={index}
                        variant="secondary"
                        className="bg-info-light/80 dark:bg-info/50 text-foreground dark:text-foreground px-4 py-2 text-sm font-semibold shadow-md"
                      >
                        {factor}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {/* Recommendation */}
              <div className="bg-background/90 dark:bg-muted/90 backdrop-blur-sm rounded-xl p-8 border border-border/50 shadow-lg">
                <h3 className="font-bold mb-6 flex items-centre gap-3 text-foreground">
                  <Activity className="h-6 w-6 text-primary" />
                  Recommendation
                </h3>
                <p className="text-base text-foreground/90 leading-relaxed">{results.recommendation}</p>
              </div>

              {/* Next Steps */}
              <div className="bg-background/90 dark:bg-muted/90 backdrop-blur-sm rounded-xl p-8 border border-border/50 shadow-lg">
                <h3 className="font-bold mb-6 flex items-centre gap-3 text-foreground">
                  <ArrowRight className="h-6 w-6 text-primary" />
                  Recommended Next Steps
                </h3>
                <ul className="space-y-4">
                  {results.nextSteps.map((step, index) => (
                    <li key={index} className="flex items-start gap-4">
                      <div className="w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-centre justify-centre text-sm font-bold mt-1 flex-shrink-0 shadow-lg">
                        {index + 1}
                      </div>
                      <span className="text-base text-foreground/90 leading-relaxed">{step}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Important Disclaimer */}
              <div className="bg-info/80 dark:bg-info/30 border border-info/70 dark:border-info/50 rounded-xl p-6 backdrop-blur-sm shadow-lg">
                <div className="flex items-start gap-4">
                  <AlertTriangle className="h-6 w-6 text-info dark:text-info mt-1 flex-shrink-0" />
                  <div>
                    <h4 className="font-bold text-info dark:text-info mb-3">Important Disclaimer</h4>
                    <p className="text-sm text-info dark:text-info leading-relaxed">
                      This assessment tool is for educational purposes only and does not replace professional medical diagnosis.
                      Cervical myelopathy can cause permanent neurological damage and requires prompt medical evaluation.
                      Always consult with qualified healthcare professionals for proper evaluation and treatment.
                    </p>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className={cn("flex gap-4", deviceInfo.isMobile ? "flex-col" : "flex-row justify-center")}>
                <Button
                  size="lg"
                  className="px-8 py-4 font-semibold shadow-xl hover:shadow-xl transition-all duration-300 hover:scale-105"
                >
                  <Brain className="mr-3 h-5 w-5" />
                  Find a Specialist
                </Button>
                <Button
                  variant="outline"
                  size="lg"
                  className="px-8 py-4 font-semibold border-2 hover:shadow-lg transition-all duration-300"
                  onClick={() => {
                    setShowResults(false);
                    setCurrentQuestion(0);
                    setAnswers({});
                  }}
                >
                  Retake Assessment
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>
    );
  }

  return (
    <section
      className={cn(
        "py-20 bg-gradient-to-br from-muted/20 via-muted/30 to-muted/20 border-y border-border/50",
        deviceInfo.isMobile ? "px-4" : ""
      )}
      aria-labelledby={`${assessmentId}-title`}
    >
      <div className="container max-w-4xl">
        <div className="text-centre mb-16">
          <h2
            id={`${assessmentId}-title`}
            className={cn(
              "font-bold text-foreground mb-6 leading-tight",
              deviceInfo.isMobile ? "text-2xl" : "text-3xl lg:text-4xl"
            )}
          >
            Cervical Myelopathy Symptom Assessment
          </h2>
          <p className={cn(
            "text-foreground/80 leading-relaxed max-w-3xl mx-auto",
            deviceInfo.isMobile ? "text-base" : "text-lg"
          )}>
            Answer these questions to assess symptoms and receive personalised guidance about when to seek medical attention
          </p>
        </div>

        {/* Progress */}
        <div className="mb-12">
          <div className="flex justify-between items-centre mb-4">
            <span className="text-base font-bold text-foreground">Assessment Progress</span>
            <span className="text-sm text-foreground/70 font-semibold bg-background/60 dark:section-background-muted section-spacing px-3  rounded-full">
              {currentQuestion + 1} of {assessmentQuestions.length}
            </span>
          </div>
          <Progress value={progress} className="h-4 bg-muted/50 shadow-inner" />
        </div>

        {/* Question Card */}
        <Card className="medical-card/95 backdrop-blur-sm border border-border/50 shadow-xl hover:shadow-3xl transition-all duration-300">
          <CardHeader className="pb-8">
            <CardTitle className="flex items-centre gap-4 text-foreground mb-4">
              <div className="p-3 rounded-xl bg-primary/10 border border-primary/20">
                <TrendingUp className="h-6 w-6 text-primary" />
              </div>
              <span className="font-bold text-xl">Question {currentQuestion + 1}</span>
            </CardTitle>
            <CardDescription className={cn(
              "text-foreground font-bold leading-relaxed",
              deviceInfo.isMobile ? "text-lg" : "text-xl lg:text-2xl"
            )}>
              {assessmentQuestions[currentQuestion]?.question}
            </CardDescription>
            {assessmentQuestions[currentQuestion]?.description && (
              <div className="text-foreground/80 text-base leading-relaxed mt-4 bg-info/80 dark:bg-info/30 border border-info/70 dark:border-info/50 p-4 rounded-xl">
                <div className="flex items-start gap-3">
                  <Brain className="h-5 w-5 text-info dark:text-info mt-0.5 flex-shrink-0" />
                  <span className="font-medium">{assessmentQuestions[currentQuestion].description}</span>
                </div>
              </div>
            )}
          </CardHeader>
          <CardContent className="pt-0 pb-8">
            <RadioGroup
              value={answers[assessmentQuestions[currentQuestion]?.id] || ''}
              onValueChange={handleAnswer}
              className="space-y-5"
            >
              {assessmentQuestions[currentQuestion]?.options.map((option) => (
                <div
                  key={option.value}
                  className="flex items-start space-x-4 p-5 rounded-xl border border-border/50 bg-background/70 dark:bg-muted/70 backdrop-blur-sm hover:bg-background/90 dark:hover:bg-muted/90 hover:border-primary/40 hover:shadow-lg transition-all duration-300 hover:scale-[1.02]"
                >
                  <RadioGroupItem value={option.value} id={option.value} className="mt-2 border-2 w-5 h-5" />
                  <Label
                    htmlFor={option.value}
                    className="font-semibold cursor-pointer flex-1 text-foreground leading-relaxed text-base hover:text-primary transition-colors duration-200"
                  >
                    {option.label}
                  </Label>
                </div>
              ))}
            </RadioGroup>
          </CardContent>
        </Card>

        {/* Navigation */}
        <div className={cn("flex justify-between mt-12", deviceInfo.isMobile ? "flex-col gap-6" : "")}>
          <Button
            variant="outline"
            onClick={handlePrevious}
            disabled={currentQuestion === 0}
            className={cn(
              "px-8 py-4 font-bold border-2 hover:shadow-xl transition-all duration-300 hover:scale-105",
              deviceInfo.isMobile ? "order-2 w-full" : ""
            )}
          >
            <ArrowLeft className="mr-3 h-5 w-5" />
            Previous Question
          </Button>
          <Button
            onClick={handleNext}
            disabled={!canProceed}
            className={cn(
              "px-8 py-4 font-bold shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105",
              deviceInfo.isMobile ? "order-1 w-full" : ""
            )}
          >
            {isLastQuestion ? 'View Assessment Results' : 'Next Question'}
            <ArrowRight className="ml-3 h-5 w-5" />
          </Button>
        </div>
      </div>
    </section>
  );
}

export default MyelopathySymptomAssessment;
