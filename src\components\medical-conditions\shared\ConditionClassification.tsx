import React from 'react';

import SafeImage from '@/components/SafeImage';
import { Card } from '@/components/ui/card';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface ClassificationItem {
  title: string;
  description: string;
  details: string[];
  colorScheme?: 'blue' | 'green' | 'purple' | 'orange' | 'red' | 'teal' | 'yellow';
}

interface ClassificationTab {
  id: string;
  label: string;
  title: string;
  description?: string;
  items?: ClassificationItem[];
  content?: React.ReactNode;
  image?: {
    src: string;
    alt: string;
    fallback?: string;
  };
}

interface ConditionClassificationProps {
  title: string;
  tabs: ClassificationTab[];
  defaultTab?: string;
  className?: string;
}

const colorSchemes = {
  blue: 'border-primary/50 bg-primary/10 text-primary',
  green: 'border-success/50 bg-success-light text-success',
  purple: 'border-medical-blue/50 bg-medical-blue-light text-medical-blue',
  orange: 'border-info/50 bg-info-light text-info',
  red: 'border-border bg-muted-light text-foreground',
  teal: 'border-info/50 bg-info-light text-info',
  yellow: 'border-info/50 bg-info-light text-info'
};

export function ConditionClassification({
  title,
  tabs,
  defaultTab,
  className
}: ConditionClassificationProps) {
  const deviceInfo = useDeviceDetection();

  return (
    <div className={cn(
      "bg-muted/30",
      deviceInfo.isMobile ? "py-8" : "py-16",
      className
    )}><div className={cn("container", deviceInfo.isMobile ? "px-4" : "")}>
        <h2 className={cn(
          "text-enhanced-heading font-bold text-center mb-12",
          deviceInfo.isMobile ? "text-2xl mb-8" : "text-3xl"
        )}>
          {title}
        </h2>

        <Tabs defaultValue={defaultTab || tabs[0]?.id} className="w-full max-w-4xl mx-auto">
          <TabsList className={cn(
            "grid mb-8",
            deviceInfo.isMobile ? "grid-cols-1 h-auto" : `grid-cols-${Math.min(tabs.length, 3)}`
          )}>
            {tabs.map((tab) => (
              <TabsTrigger
                key={tab.id}
                value={tab.id}
                className={cn(
                  "text-center",
                  deviceInfo.isMobile ? "py-3" : "py-3"
                )}
              >
                {tab.label}
              </TabsTrigger>
            ))}
          </TabsList>

          {tabs.map((tab) => (
            <TabsContent key={tab.id} value={tab.id} className="medical-card p-6 rounded-lg shadow-md">
              <div className="space-y-6">
                <h3 className="text-enhanced-heading text-xl font-bold text-center">{tab.title}</h3>

                {tab.description && (
                  <p className="text-enhanced-body text-center">{tab.description}</p>
                )}

                {tab.items && (
                  <div className={cn("grid", tab.items.length <= 2 ? "grid-cols-1 lg:grid-cols-2" : tab.items.length <= 4 ? "grid-cols-1 lg:grid-cols-2" : "grid-cols-1 md:grid-cols-2")}>{tab.items.map((item, index) => (
                      <Card
                        key={index}
                        className={cn(
                          "p-4",
                          item.colorScheme ? colorSchemes[item.colorScheme] : colorSchemes.blue
                        )}
                      >
                        <h4 className="font-semibold mb-2">
                          {item.title}
                        </h4>
                        <p className="text-sm mb-3">{item.description}</p>
                        <ul className="text-sm space-y-1">
                          {item.details.map((detail, detailIndex) => (
                            <li key={detailIndex}>• {detail}</li>
                          ))}
                        </ul>
                      </Card>
                    ))}
                  </div>
                )}

                {tab.content && (
                  <div>{tab.content}</div>
                )}

                {tab.image && (
                  <div className="text-center mt-6">
                    <SafeImage
                      src={tab.image.src}
                      alt={tab.image.alt}
                      className="w-full max-w-sm mx-auto h-auto rounded-lg"
                      fallbackSrc={tab.image.fallback || "https://images.unsplash.com/photo-1559757148-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"}
                    />
                  </div>
                )}
              </div>
            </TabsContent>
          ))}
        </Tabs>
      </div>        </div>
  );
}
