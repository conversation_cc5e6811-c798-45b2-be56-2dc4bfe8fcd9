import { <PERSON>, Lock, UserCheck, FileText } from 'lucide-react';
import React from 'react';

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface PrivacyPrinciple {
  title: string;
  description: string;
}

interface PrivacySectionProps {
  title: string;
  subtitle: string;
  description: string[];
  principles: PrivacyPrinciple[];
}

const PrivacySection: React.FC<PrivacySectionProps> = ({
  title,
  subtitle,
  description,
  principles
}) => {
  const deviceInfo = useDeviceDetection();

  const iconMap = {
    0: Shield,
    1: UserCheck,
    2: Lock,
    3: FileText
  };

  return (
    <section className="py-16 bg-muted">
      <div className="container">
        <div className="text-centre mb-12">
          <div className="flex justify-centre mb-4">
            <div className="p-3 bg-primary/10 rounded-full">
              <Shield className="h-8 w-8 text-primary" />
            </div>
          </div>
          <h2 className="text-enhanced-heading text-3xl font-bold mb-4">{title}</h2>
          <p className="text-xl text-muted-foreground mb-6">
            {subtitle}
          </p>
          <div className="max-w-4xl mx-auto space-y-4">
            {description.map((paragraph, index) => (
              <p key={index} className="text-muted-foreground">
                {paragraph}
              </p>
            ))}
          </div>
        </div>

        <div className={cn(
          "grid gap-6 mb-12",
          deviceInfo.isMobile 
            ? "grid-cols-1" 
            : "grid-cols-1 md:grid-cols-2"
        )}>
          {principles.map((principle, index) => {
            const IconComponent = iconMap[index as keyof typeof iconMap] || Shield;
            
            return (
              <Card key={index} className="shadow-lg hover:shadow-xl transition-all duration-300 border-0 bg-background/80 backdrop-blur-sm">
                <CardHeader>
                  <div className="flex items-centre gap-3 mb-2">
                    <div className="p-2 bg-primary/10 rounded-lg">
                      <IconComponent className="h-5 w-5 text-primary" />
                    </div>
                    <CardTitle className="text-lg text-muted-foreground">
                      {principle.title}
                    </CardTitle>
                  </div>
                </CardHeader>
                
                <CardContent>
                  <p className="text-muted-foreground leading-relaxed">
                    {principle.description}
                  </p>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Trust Statement */}
        <div className="text-center">
          <Card className="max-w-3xl mx-auto shadow-xl bg-gradient-to-r from-primary/5 to-info-light border-primary/20">
            <CardContent className="p-8">
              <div className="flex justify-center mb-4">
                <div className="p-3 bg-primary/20 rounded-full">
                  <Lock className="h-6 w-6 text-primary" />
                </div>
              </div>
              <h3 className="text-enhanced-heading text-2xl font-bold mb-4 text-muted-foreground">
                Your Trust is Our Priority
              </h3>
              <p className="text-muted-foreground mb-6 leading-relaxed">
                We understand that sharing personal health information requires trust. Our commitment to 
                maintaining the highest standards of privacy and confidentiality ensures that you can 
                focus on your health and recovery with complete peace of mind.
              </p>
              <div className="flex flex-wrap justify-centre gap-3">
                <div className="flex items-centre gap-2 bg-background/60 px-4 py-2 rounded-full">
                  <Shield className="h-4 w-4 text-success" />
                  <span className="text-sm font-medium text-muted-foreground">Secure Storage</span>
                </div>
                <div className="flex items-centre gap-2 bg-background/60 px-4 py-2 rounded-full">
                  <UserCheck className="h-4 w-4 text-info" />
                  <span className="text-sm font-medium text-muted-foreground">Consent-Based Sharing</span>
                </div>
                <div className="flex items-centre gap-2 bg-background/60 px-4 py-2 rounded-full">
                  <FileText className="h-4 w-4 text-medical-blue" />
                  <span className="text-sm font-medium text-muted-foreground">Professional Standards</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Contact for Privacy Concerns */}
        <div className="mt-8 text-centre">
          <p className="text-sm text-muted-foreground">
            If you have any questions about our privacy practices or wish to discuss your privacy preferences, 
            please don't hesitate to contact our office at{' '}
            <a href="tel:**********" className="text-primary hover:underline font-medium">
              (03) 9008 4200
            </a>
          </p>
        </div>
      </div>
    </section>
  );
};

export default PrivacySection;
