import { 
  <PERSON><PERSON><PERSON>, 
  Target, 
  Zap, 
  Activity, 
  Clock, 
  CheckCircle,
  <PERSON><PERSON><PERSON><PERSON>gle,
  Play,
  RotateCcw,
  <PERSON><PERSON>dingUp,
  Heart,
  Shield,
  Award,
  Eye
} from "lucide-react";
import React, { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface Exercise {
  id: string;
  name: string;
  description: string;
  instructions: string[];
  duration: string;
  frequency: string;
  difficulty: 'Beginner' | 'Intermediate' | 'Advanced';
  benefits: string[];
  precautions: string[];
  progressions: string[];
}

interface ExerciseCategory {
  id: string;
  title: string;
  icon: React.ComponentType<any>;
  description: string;
  exercises: Exercise[];
}

interface NerveTumorExerciseSectionProps {
  className?: string;
}

const exerciseCategories: ExerciseCategory[] = [
  {
    id: 'nerve-gliding',
    title: 'Nerve Gliding Exercises',
    icon: Zap,
    description: 'Gentle mobilisation exercises to maintain nerve flexibility and reduce adhesions',
    exercises: [
      {
        id: 'median-nerve-gliding',
        name: 'Median Nerve Gliding',
        description: 'Gentle mobilisation for median nerve tumors and post-surgical recovery',
        instructions: [
          'Start with arm at side, elbow bent 90 degrees',
          'Make a fist with thumb inside fingers',
          'Slowly extend wrist and straighten fingers',
          'Extend elbow while maintaining wrist extension',
          'Add gentle shoulder abduction if tolerated',
          'Hold end position for 5 seconds, return slowly'
        ],
        duration: '5-10 minutes',
        frequency: '2-3 times daily',
        difficulty: 'Beginner',
        benefits: [
          'Maintains nerve mobility and flexibility',
          'Reduces post-surgical adhesions',
          'Improves nerve blood flow',
          'Prevents stiffness and contractures'
        ],
        precautions: [
          'Stop if sharp pain or electric sensations occur',
          'Avoid forceful or aggressive movements',
          'Progress gradually and listen to your body',
          'Consult therapist if symptoms worsen'
        ],
        progressions: [
          'Increase range of motion gradually',
          'Add gentle nerve tensioning',
          'Combine with functional activities',
          'Progress to more complex movement patterns'
        ]
      },
      {
        id: 'ulnar-nerve-gliding',
        name: 'Ulnar Nerve Gliding',
        description: 'Specific exercises for ulnar nerve tumors and cubital tunnel issues',
        instructions: [
          'Start with arm at side, palm facing forward',
          'Bend elbow and bring hand toward ear',
          'Slowly straighten elbow while tilting head away',
          'Add wrist flexion and extension',
          'Return to starting position slowly',
          'Perform smooth, controlled movements'
        ],
        duration: '5-10 minutes',
        frequency: '2-3 times daily',
        difficulty: 'Beginner',
        benefits: [
          'Improves ulnar nerve mobility',
          'Reduces cubital tunnel symptoms',
          'Maintains elbow and wrist flexibility',
          'Prevents nerve adhesions'
        ],
        precautions: [
          'Avoid excessive elbow flexion',
          'Stop if numbness or tingling increases',
          'Don\'t force movements beyond comfort',
          'Monitor for increased symptoms'
        ],
        progressions: [
          'Increase movement amplitude',
          'Add cervical spine movements',
          'Combine with strengthening exercises',
          'Progress to functional activities'
        ]
      }
    ]
  },
  {
    id: 'strengthening',
    title: 'Strengthening Exercises',
    icon: Dumbbell,
    description: 'Gentle strengthening to maintain muscle function and prevent atrophy',
    exercises: [
      {
        id: 'grip-strengthening',
        name: 'Progressive Grip Strengthening',
        description: 'Gradual strengthening for hand and forearm muscles',
        instructions: [
          'Start with soft stress ball or putty',
          'Squeeze gently and hold for 5 seconds',
          'Release slowly and completely',
          'Progress to firmer resistance as tolerated',
          'Perform different grip patterns',
          'Monitor for fatigue or increased symptoms'
        ],
        duration: '10-15 minutes',
        frequency: '2-3 times daily',
        difficulty: 'Beginner',
        benefits: [
          'Maintains hand and forearm strength',
          'Improves functional grip capacity',
          'Prevents muscle atrophy',
          'Enhances fine motor control'
        ],
        precautions: [
          'Start with very light resistance',
          'Avoid gripping exercises if tumor causes pain',
          'Stop if symptoms worsen',
          'Progress very gradually'
        ],
        progressions: [
          'Increase resistance gradually',
          'Add different grip positions',
          'Combine with functional tasks',
          'Progress to dynamic strengthening'
        ]
      },
      {
        id: 'isometric-strengthening',
        name: 'Isometric Muscle Strengthening',
        description: 'Static strengthening exercises that don\'t stress the tumor site',
        instructions: [
          'Position limb in comfortable, neutral position',
          'Contract muscles without moving joints',
          'Hold contraction for 5-10 seconds',
          'Relax completely between contractions',
          'Focus on muscles not directly affected by tumor',
          'Breathe normally during exercises'
        ],
        duration: '10-15 minutes',
        frequency: '3-4 times weekly',
        difficulty: 'Intermediate',
        benefits: [
          'Maintains muscle strength safely',
          'Doesn\'t stress tumor or surgical site',
          'Improves muscle endurance',
          'Can be performed anywhere'
        ],
        precautions: [
          'Avoid contractions that increase tumor pressure',
          'Stop if pain or symptoms increase',
          'Don\'t hold breath during contractions',
          'Start with short hold times'
        ],
        progressions: [
          'Increase hold time gradually',
          'Add more muscle groups',
          'Progress to dynamic exercises',
          'Combine with functional movements'
        ]
      }
    ]
  },
  {
    id: 'range-of-motion',
    title: 'Range of Motion',
    icon: Target,
    description: 'Maintain joint flexibility and prevent stiffness',
    exercises: [
      {
        id: 'passive-rom',
        name: 'Passive Range of Motion',
        description: 'Gentle joint movements to maintain flexibility',
        instructions: [
          'Use unaffected hand to move affected limb',
          'Move joints slowly through comfortable range',
          'Hold end positions for 10-15 seconds',
          'Perform all major joint movements',
          'Focus on smooth, controlled movements',
          'Stop at first sign of discomfort'
        ],
        duration: '10-15 minutes',
        frequency: '2-3 times daily',
        difficulty: 'Beginner',
        benefits: [
          'Maintains joint flexibility',
          'Prevents contractures and stiffness',
          'Improves circulation',
          'Reduces pain and stiffness'
        ],
        precautions: [
          'Never force movements',
          'Respect pain and tissue resistance',
          'Be extra gentle around tumor site',
          'Stop if symptoms worsen'
        ],
        progressions: [
          'Increase range gradually',
          'Progress to active-assisted movements',
          'Add active range of motion',
          'Combine with strengthening'
        ]
      }
    ]
  },
  {
    id: 'functional-training',
    title: 'Functional Training',
    icon: Activity,
    description: 'Task-specific exercises to improve daily function',
    exercises: [
      {
        id: 'activities-of-daily-living',
        name: 'Activities of Daily Living Practice',
        description: 'Practice everyday tasks with adaptive techniques',
        instructions: [
          'Identify challenging daily activities',
          'Practice with adaptive equipment if needed',
          'Break complex tasks into smaller steps',
          'Use energy conservation techniques',
          'Modify techniques to accommodate limitations',
          'Practice regularly to build confidence'
        ],
        duration: '15-20 minutes',
        frequency: 'Daily',
        difficulty: 'Intermediate',
        benefits: [
          'Improves independence in daily tasks',
          'Builds confidence and self-efficacy',
          'Develops compensatory strategies',
          'Enhances quality of life'
        ],
        precautions: [
          'Start with simple tasks',
          'Use adaptive equipment when needed',
          'Take breaks to prevent fatigue',
          'Modify activities as needed'
        ],
        progressions: [
          'Increase task complexity',
          'Reduce use of adaptive equipment',
          'Add time constraints',
          'Practice in different environments'
        ]
      }
    ]
  }
];

const NerveTumorExerciseSection: React.FC<NerveTumorExerciseSectionProps> = ({ className }) => {
  const deviceInfo = useDeviceDetection();
  const [selectedCategory, setSelectedCategory] = useState<string>('nerve-gliding');

  const getDifficultyColour = (difficulty: string) => {
    switch (difficulty) {
      case 'Beginner': return 'bg-success-light text-success border border-success/30';
      case 'Intermediate': return 'bg-info-light text-info border border-info/30';
      case 'Advanced': return 'bg-muted-light text-foreground border border-border/30';
      default: return 'bg-muted text-muted-foreground';
    }
  };

  return (
    <section className={cn(
      "section-background-alt border-y border-border/50",
      deviceInfo.isMobile ? "py-16" : "py-24",
      className
    )}>
      <div className="container">
        {/* Section Header */}
        <div className="text-centre mb-20">
          <Badge variant="info" className="mb-6">
            <Dumbbell className="w-4 h-4 mr-2" />
            Exercise & Therapy
          </Badge>
          <h2 className={cn(
            "font-bold text-foreground mb-8 leading-tight",
            deviceInfo.isMobile ? "text-2xl" : "text-3xl lg:text-4xl"
          )}>
            Exercise and Rehabilitation Guide
          </h2>
          <p className={cn(
            "text-foreground/80 max-w-4xl mx-auto leading-relaxed font-medium",
            deviceInfo.isMobile ? "text-base" : "text-lg"
          )}>
            Comprehensive exercise program designed to maintain nerve function, prevent complications, 
            and optimise recovery for individuals with peripheral nerve tumors
          </p>
        </div>

        {/* Exercise Category Tabs */}
        <Tabs value={selectedCategory} onValueChange={setSelectedCategory} className="w-full">
          <TabsList className={cn(
            "grid w-full mb-12",
            deviceInfo.isMobile ? "grid-cols-2 h-auto" : "grid-cols-4 h-14"
          )}>
            {exerciseCategories.map((category) => {
              const IconComponent = category.icon;
              return (
                <TabsTrigger 
                  key={category.id} 
                  value={category.id}
                  className={cn(
                    "flex items-centre gap-2 font-medium",
                    deviceInfo.isMobile ? "flex-col py-3 px-2 text-xs" : "text-sm"
                  )}
                >
                  <IconComponent className={cn(
                    deviceInfo.isMobile ? "w-4 h-4" : "w-5 h-5"
                  )} />
                  <span className={deviceInfo.isMobile ? "text-centre" : ""}>
                    {category.title}
                  </span>
                </TabsTrigger>
              );
            })}
          </TabsList>

          {/* Category Content */}
          {exerciseCategories.map((category) => (
            <TabsContent key={category.id} value={category.id} className="space-y-8">
              {/* Category Description */}
              <Card className="medical-card">
                <CardHeader>
                  <CardTitle className="text-enhanced-heading flex items-centre gap-3">
                    <category.icon className="w-5 h-5 text-primary" />
                    {category.title}
                  </CardTitle>
                  <p className="text-enhanced-body">{category.description}</p>
                </CardHeader>
              </Card>

              {/* Exercises */}
              <div className="space-y-8">
                {category.exercises.map((exercise) => (
                  <Card key={exercise.id} className="medical-card">
                    <CardHeader>
                      <div className="flex items-start justify-between">
                        <CardTitle className="text-enhanced-heading">{exercise.name}</CardTitle>
                        <Badge className={getDifficultyColour(exercise.difficulty)}>
                          {exercise.difficulty}
                        </Badge>
                      </div>
                      <p className="text-enhanced-body">{exercise.description}</p>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      {/* Exercise Details */}
                      <div className={cn(
                        "grid gap-4",
                        deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-3"
                      )}>
                        <div className="bg-info/5 border border-info/20 rounded-lg p-4">
                          <Clock className="w-5 h-5 text-info mb-2" />
                          <h5 className="text-enhanced-caption font-medium">Duration</h5>
                          <p className="text-enhanced-body text-sm">{exercise.duration}</p>
                        </div>
                        <div className="bg-success/5 border border-success/20 rounded-lg p-4">
                          <RotateCcw className="w-5 h-5 text-success mb-2" />
                          <h5 className="text-enhanced-caption font-medium">Frequency</h5>
                          <p className="text-enhanced-body text-sm">{exercise.frequency}</p>
                        </div>
                        <div className="bg-primary/5 border border-primary/20 rounded-lg p-4">
                          <Target className="w-5 h-5 text-primary mb-2" />
                          <h5 className="text-enhanced-caption font-medium">Level</h5>
                          <p className="text-enhanced-body text-sm">{exercise.difficulty}</p>
                        </div>
                      </div>

                      {/* Instructions */}
                      <div>
                        <h4 className="text-enhanced-subheading font-semibold mb-3 flex items-centre gap-2">
                          <Play className="w-4 h-4 text-primary" />
                          Step-by-Step Instructions
                        </h4>
                        <ol className="space-y-2">
                          {exercise.instructions.map((instruction, index) => (
                            <li key={index} className="flex items-start gap-3">
                              <div className="flex-shrink-0 w-6 h-6 rounded-full bg-primary/10 border border-primary/20 flex items-centre justify-centre">
                                <span className="text-primary font-semibold text-xs">{index + 1}</span>
                              </div>
                              <span className="text-enhanced-body text-sm">{instruction}</span>
                            </li>
                          ))}
                        </ol>
                      </div>

                      {/* Benefits and Precautions */}
                      <div className={cn(
                        "grid gap-6",
                        deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2"
                      )}>
                        <div>
                          <h4 className="text-enhanced-subheading font-semibold mb-3 flex items-centre gap-2">
                            <CheckCircle className="w-4 h-4 text-success" />
                            Benefits
                          </h4>
                          <ul className="space-y-2">
                            {exercise.benefits.map((benefit, index) => (
                              <li key={index} className="flex items-start gap-2">
                                <CheckCircle className="w-4 h-4 text-success mt-0.5 flex-shrink-0" />
                                <span className="text-enhanced-body text-sm">{benefit}</span>
                              </li>
                            ))}
                          </ul>
                        </div>

                        <div>
                          <h4 className="text-enhanced-subheading font-semibold mb-3 flex items-centre gap-2">
                            <AlertTriangle className="w-4 h-4 text-foreground" />
                            Precautions
                          </h4>
                          <ul className="space-y-2">
                            {exercise.precautions.map((precaution, index) => (
                              <li key={index} className="flex items-start gap-2">
                                <AlertTriangle className="w-4 h-4 text-foreground mt-0.5 flex-shrink-0" />
                                <span className="text-enhanced-body text-sm">{precaution}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>

                      {/* Progressions */}
                      <div>
                        <h4 className="text-enhanced-subheading font-semibold mb-3 flex items-centre gap-2">
                          <TrendingUp className="w-4 h-4 text-info" />
                          Exercise Progressions
                        </h4>
                        <ul className="space-y-2">
                          {exercise.progressions.map((progression, index) => (
                            <li key={index} className="flex items-start gap-2">
                              <div className="w-1.5 h-1.5 rounded-full bg-info mt-2 flex-shrink-0" />
                              <span className="text-enhanced-body text-sm">{progression}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>
          ))}
        </Tabs>

        {/* Exercise Program Guidelines */}
        <Card className="medical-card mt-12">
          <CardHeader>
            <CardTitle className="text-enhanced-heading flex items-center gap-3">
              <Award className="w-5 h-5 text-primary" />
              Exercise Program Guidelines for Nerve Tumors
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className={cn(
              "grid gap-6",
              deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-1 md:grid-cols-3"
            )}>
              <div className="text-center">
                <div className="p-4 rounded-xl bg-success/10 border border-success/20 mb-4">
                  <Shield className="w-8 h-8 text-success mx-auto" />
                </div>
                <h4 className="text-enhanced-subheading font-semibold mb-2">Gentle Approach</h4>
                <p className="text-enhanced-body text-sm">Always use gentle, controlled movements and avoid activities that increase pressure on the tumor</p>
              </div>
              <div className="text-centre">
                <div className="p-4 rounded-xl bg-info/10 border border-info/20 mb-4">
                  <Eye className="w-8 h-8 text-info mx-auto" />
                </div>
                <h4 className="text-enhanced-subheading font-semibold mb-2">Monitor Symptoms</h4>
                <p className="text-enhanced-body text-sm">Watch for changes in symptoms and stop exercises if pain or neurological symptoms worsen</p>
              </div>
              <div className="text-centre">
                <div className="p-4 rounded-xl bg-muted/50 border border-border/50 mb-4">
                  <Heart className="w-8 h-8 text-foreground mx-auto" />
                </div>
                <h4 className="text-enhanced-subheading font-semibold mb-2">Professional Guidance</h4>
                <p className="text-enhanced-body text-sm">Work with qualified physiotherapists familiar with nerve tumor management</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </section>
  );
};

export default NerveTumorExerciseSection;
