import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, ArrowRight, ArrowLeft, Activity, Zap, Phone } from 'lucide-react';
import React, { useState, useId } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface AssessmentQuestion {
  id: string;
  question: string;
  description?: string;
  options: Array<{
    value: string;
    label: string;
    score: number;
    urgency?: 'immediate' | 'urgent' | 'routine';
  }>;
}

interface AssessmentResult {
  totalScore: number;
  urgency: 'immediate' | 'urgent' | 'routine';
  recommendation: string;
  action: string;
  timeframe: string;
  redFlags: string[];
}

const emergencyQuestions: AssessmentQuestion[] = [
  {
    id: 'bladder-function',
    question: 'Are you experiencing problems with bladder control or urination?',
    description: 'Bladder dysfunction is a cardinal sign of cauda equina syndrome',
    options: [
      { value: 'retention', label: 'Cannot urinate at all or very little', score: 10, urgency: 'immediate' },
      { value: 'incontinence', label: 'Loss of bladder control/leaking urine', score: 8, urgency: 'immediate' },
      { value: 'difficulty', label: 'Difficulty starting or stopping urination', score: 6, urgency: 'urgent' },
      { value: 'normal', label: 'Normal bladder function', score: 0 }
    ]
  },
  {
    id: 'bowel-function',
    question: 'Are you experiencing problems with bowel control?',
    description: 'Bowel dysfunction indicates severe cauda equina compression',
    options: [
      { value: 'incontinence', label: 'Loss of bowel control/soiling', score: 10, urgency: 'immediate' },
      { value: 'no-sensation', label: 'Cannot feel when bowel is full', score: 8, urgency: 'immediate' },
      { value: 'constipation', label: 'Severe constipation or difficulty', score: 4, urgency: 'urgent' },
      { value: 'normal', label: 'Normal bowel function', score: 0 }
    ]
  },
  {
    id: 'saddle-numbness',
    question: 'Do you have numbness or loss of sensation in your perineal area (between legs)?',
    description: 'Saddle anaesthesia is a pathognomonic sign of cauda equina syndrome',
    options: [
      { value: 'complete-loss', label: 'Complete loss of sensation in perineal area', score: 10, urgency: 'immediate' },
      { value: 'partial-loss', label: 'Reduced sensation in perineal area', score: 6, urgency: 'urgent' },
      { value: 'tingling', label: 'Tingling or pins and needles', score: 3, urgency: 'urgent' },
      { value: 'normal', label: 'Normal sensation', score: 0 }
    ]
  },
  {
    id: 'leg-weakness',
    question: 'Are you experiencing weakness in your legs?',
    description: 'Progressive leg weakness indicates motor nerve involvement',
    options: [
      { value: 'severe-bilateral', label: 'Severe weakness in both legs', score: 8, urgency: 'immediate' },
      { value: 'moderate-bilateral', label: 'Moderate weakness in both legs', score: 6, urgency: 'urgent' },
      { value: 'unilateral', label: 'Weakness in one leg only', score: 4, urgency: 'urgent' },
      { value: 'normal', label: 'Normal leg strength', score: 0 }
    ]
  },
  {
    id: 'sexual-function',
    question: 'Have you noticed sudden changes in sexual function?',
    description: 'Sexual dysfunction can be an early sign of cauda equina syndrome',
    options: [
      { value: 'complete-loss', label: 'Complete loss of sexual function/sensation', score: 6, urgency: 'urgent' },
      { value: 'significant-change', label: 'Significant reduction in function', score: 4, urgency: 'urgent' },
      { value: 'mild-change', label: 'Mild changes noticed', score: 2 },
      { value: 'normal', label: 'Normal sexual function', score: 0 }
    ]
  },
  {
    id: 'back-pain',
    question: 'How would you describe your back pain?',
    description: 'Severe back pain often accompanies cauda equina syndrome',
    options: [
      { value: 'worst-ever', label: 'Worst pain I have ever experienced', score: 4, urgency: 'urgent' },
      { value: 'severe-constant', label: 'Severe and constant pain', score: 3, urgency: 'urgent' },
      { value: 'moderate', label: 'Moderate pain that comes and goes', score: 2 },
      { value: 'mild', label: 'Mild or no back pain', score: 0 }
    ]
  }
];

export function EmergencyAssessmentTool() {
  const deviceInfo = useDeviceDetection();
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [answers, setAnswers] = useState<Record<string, string>>({});
  const [showResults, setShowResults] = useState(false);
  const assessmentId = useId();

  const progress = ((currentQuestion + 1) / emergencyQuestions.length) * 100;
  const isLastQuestion = currentQuestion === emergencyQuestions.length - 1;
  const canProceed = answers[emergencyQuestions[currentQuestion]?.id];

  const handleAnswer = (value: string) => {
    setAnswers(prev => ({
      ...prev,
      [emergencyQuestions[currentQuestion].id]: value
    }));
  };

  const handleNext = () => {
    if (isLastQuestion) {
      setShowResults(true);
    } else {
      setCurrentQuestion(prev => prev + 1);
    }
  };

  const handlePrevious = () => {
    if (currentQuestion > 0) {
      setCurrentQuestion(prev => prev - 1);
    }
  };

  const calculateResults = (): AssessmentResult => {
    const totalScore = Object.entries(answers).reduce((total, [questionId, answer]) => {
      const question = emergencyQuestions.find(q => q.id === questionId);
      const option = question?.options.find(o => o.value === answer);
      return total + (option?.score || 0);
    }, 0);

    let urgency: 'immediate' | 'urgent' | 'routine';
    let recommendation: string;
    let action: string;
    let timeframe: string;
    const redFlags: string[] = [];

    // Check for immediate red flags
    const hasImmediateFlags = Object.entries(answers).some(([questionId, answer]) => {
      const question = emergencyQuestions.find(q => q.id === questionId);
      const option = question?.options.find(o => o.value === answer);
      return option?.urgency === 'immediate';
    });

    // Identify specific red flags
    if (answers['bladder-function'] && !answers['bladder-function'].includes('normal')) {
      redFlags.push('Bladder dysfunction');
    }
    if (answers['bowel-function'] && !answers['bowel-function'].includes('normal')) {
      redFlags.push('Bowel dysfunction');
    }
    if (answers['saddle-numbness'] && !answers['saddle-numbness'].includes('normal')) {
      redFlags.push('Saddle anaesthesia');
    }
    if (answers['leg-weakness'] && !answers['leg-weakness'].includes('normal')) {
      redFlags.push('Leg weakness');
    }

    if (hasImmediateFlags || totalScore >= 15) {
      urgency = 'immediate';
      recommendation = 'EMERGENCY: Your symptoms strongly suggest cauda equina syndrome, which is a surgical emergency. You need immediate medical attention.';
      action = 'Go to the emergency department immediately or call 000';
      timeframe = 'IMMEDIATE - Do not delay';
    } else if (totalScore >= 8) {
      urgency = 'urgent';
      recommendation = 'URGENT: Your symptoms are concerning and may indicate cauda equina syndrome. You need urgent medical evaluation.';
      action = 'Seek urgent medical attention within 2-4 hours';
      timeframe = 'Within 2-4 hours';
    } else if (totalScore >= 3) {
      urgency = 'urgent';
      recommendation = 'Your symptoms warrant medical evaluation to rule out cauda equina syndrome and other serious conditions.';
      action = 'Contact your GP or seek medical advice within 24 hours';
      timeframe = 'Within 24 hours';
    } else {
      urgency = 'routine';
      recommendation = 'Your symptoms are unlikely to indicate cauda equina syndrome, but continue to monitor for any changes.';
      action = 'Monitor symptoms and seek medical advice if they worsen';
      timeframe = 'Routine follow-up as needed';
    }

    return { totalScore, urgency, recommendation, action, timeframe, redFlags };
  };

  const results = showResults ? calculateResults() : null;

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'immediate': return 'bg-muted/30 text-foreground border-border';
      case 'urgent': return 'bg-info/10 text-info dark:text-info border-info/50';
      default: return 'bg-success/10 text-success dark:text-success border-success/50';
    }
  };

  const getUrgencyIcon = (urgency: string) => {
    switch (urgency) {
      case 'immediate': return <AlertTriangle className="h-5 w-5 text-foreground" />;
      case 'urgent': return <Clock className="h-5 w-5 text-info dark:text-info" />;
      default: return <Activity className="h-5 w-5 text-success dark:text-success" />;
    }
  };

  if (showResults && results) {
    return (
      <section className={cn("py-20 bg-gradient-to-br from-muted/20 via-muted/30 to-muted/20 border-y border-border/50", deviceInfo.isMobile ? "px-4" : "")}>
        <div className="container max-w-4xl">
          <Card className={cn("border-2 bg-card/95 backdrop-blur-sm shadow-2xl", getUrgencyColor(results.urgency))}>
            <CardHeader className="text-center pb-8">
              <div className="flex items-center justify-center gap-3 mb-6">
                <div className="p-3 rounded-xl bg-primary/10 border border-primary/20">
                  {getUrgencyIcon(results.urgency)}
                </div>
                <CardTitle className={cn("font-bold text-foreground", deviceInfo.isMobile ? "text-xl" : "text-2xl lg:text-3xl")}>
                  Emergency Assessment Results
                </CardTitle>
              </div>
              <CardDescription className={cn("text-foreground/80 font-medium", deviceInfo.isMobile ? "text-base" : "text-lg")}>
                Based on your responses, here's your urgent care recommendation
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-8">
              {/* Urgency Level */}
              <div className="text-centre p-8 medical-card/70 backdrop-blur-sm rounded-xl border border-border/30 shadow-lg">
                <div className="flex items-centre justify-centre gap-6 mb-6">
                  <div className="text-centre">
                    <div className="text-enhanced-heading text-4xl font-bold text-primary mb-2">{results.totalScore}</div>
                    <div className="text-sm font-semibold text-foreground/70">Risk Score</div>
                  </div>
                  <div>
                    <Badge className={cn("text-sm font-bold px-4 py-2", getUrgencyColor(results.urgency))}>
                      {results.urgency.toUpperCase()} ATTENTION
                    </Badge>
                  </div>
                </div>
              </div>

              {/* Red Flags */}
              {results.redFlags.length > 0 && (
                <div className="bg-muted/50 dark:bg-muted/30 border border-border rounded-xl p-6 shadow-lg">
                  <h3 className="font-bold mb-4 flex items-centre gap-3 text-foreground">
                    <div className="p-2 rounded-lg bg-muted/40 dark:bg-muted/30">
                      <AlertTriangle className="h-5 w-5 text-foreground" />
                    </div>
                    Red Flag Symptoms Identified
                  </h3>
                  <div className="flex flex-wrap gap-3">
                    {results.redFlags.map((flag, index) => (
                      <Badge key={index} className="bg-muted-light dark:bg-muted-light text-foreground font-semibold px-3 py-1">
                        {flag}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {/* Recommendation */}
              <div className="bg-background/70 dark:bg-muted/70 backdrop-blur-sm rounded-xl p-8 border border-border/30 shadow-lg">
                <h3 className="font-bold mb-4 flex items-centre gap-3 text-foreground">
                  <div className="p-3 rounded-xl bg-primary/10 border border-primary/20">
                    <Brain className="h-6 w-6 text-primary" />
                  </div>
                  Medical Recommendation
                </h3>
                <p className="text-base font-medium text-foreground/90 mb-6 leading-relaxed">{results.recommendation}</p>
                <div className="bg-info/80 dark:bg-info/30 border border-info/70 dark:border-info/50 rounded-xl p-5 shadow-md">
                  <p className="text-base font-bold text-info dark:text-info mb-2">{results.action}</p>
                  <p className="text-sm text-info dark:text-info font-semibold">Timeframe: {results.timeframe}</p>
                </div>
              </div>

              {/* Emergency Contact */}
              {results.urgency === 'immediate' && (
                <div className="bg-muted/50 dark:bg-muted/30 border border-border rounded-xl p-8 shadow-lg">
                  <h3 className="font-bold mb-6 flex items-centre gap-3 text-foreground">
                    <div className="p-3 rounded-xl bg-muted/40 dark:bg-muted/30">
                      <Phone className="h-6 w-6 text-foreground" />
                    </div>
                    Emergency Contact Information
                  </h3>
                  <div className="space-y-4">
                    <div className="medical-card/70 backdrop-blur-sm rounded-lg p-4 border border-border/30">
                      <p className="text-base font-bold text-foreground">
                        <strong>Emergency Services:</strong> Call 000 immediately
                      </p>
                    </div>
                    <div className="bg-background/70 dark:bg-muted/70 backdrop-blur-sm rounded-lg p-4 border border-border/30">
                      <p className="text-base font-bold text-foreground dark:text-foreground">
                        <strong>Nearest Emergency Department:</strong> Go immediately - do not wait
                      </p>
                    </div>
                    <div className="bg-background/70 dark:bg-muted/70 backdrop-blur-sm rounded-lg p-4 border border-border/30">
                      <p className="text-base font-bold text-foreground dark:text-foreground">
                        <strong>Tell them:</strong> "I may have cauda equina syndrome - this is a surgical emergency"
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {/* Important Disclaimer */}
              <div className="bg-info/80 dark:bg-info/30 border border-info/70 dark:border-info/50 rounded-xl p-6 shadow-lg">
                <div className="flex items-start gap-4">
                  <div className="p-2 rounded-lg bg-info dark:bg-info/50 flex-shrink-0">
                    <AlertTriangle className="h-6 w-6 text-info dark:text-info" />
                  </div>
                  <div>
                    <h4 className="font-bold text-info dark:text-info mb-3 text-lg">Critical Medical Disclaimer</h4>
                    <p className="text-base text-info dark:text-info font-medium leading-relaxed">
                      This assessment tool is for educational purposes only and does not replace emergency medical evaluation.
                      Cauda equina syndrome is a surgical emergency. If you have any of these symptoms, seek immediate medical attention.
                    </p>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className={cn("flex gap-6 pt-4", deviceInfo.isMobile ? "flex-col" : "flex-row justify-centre")}>
                {results.urgency === 'immediate' ? (
                  <Button size="lg" className="bg-muted hover:bg-muted dark:bg-muted dark:hover:bg-muted font-bold px-8 py-4 shadow-xl hover:shadow-xl transition-all duration-300 hover:scale-105">
                    <Phone className="mr-3 h-5 w-5" />
                    Call 000 Now
                  </Button>
                ) : (
                  <Button size="lg" className="font-bold px-8 py-4 shadow-xl hover:shadow-xl transition-all duration-300 hover:scale-105">
                    <Brain className="mr-3 h-5 w-5" />
                    Find Medical Care
                  </Button>
                )}
                <Button variant="outline" size="lg" className="font-bold px-8 py-4 border-2 hover:shadow-xl transition-all duration-300 hover:scale-105" onClick={() => {
                  setShowResults(false);
                  setCurrentQuestion(0);
                  setAnswers({});
                }}>
                  Retake Assessment
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>
    );
  }

  return (
    <section
      className={cn("py-20 bg-gradient-to-br from-muted/20 via-muted/30 to-muted/20 border-y border-border/50", deviceInfo.isMobile ? "px-4" : "")}
      aria-labelledby={`${assessmentId}-title`}
    >
      <div className="container max-w-4xl">
        <div className="text-centre mb-16">
          <h2
            id={`${assessmentId}-title`}
            className={cn("font-bold text-foreground mb-6 leading-tight", deviceInfo.isMobile ? "text-2xl" : "text-3xl lg:text-4xl")}
          >
            Emergency Cauda Equina Assessment
          </h2>
          <p className={cn("text-foreground/80 leading-relaxed max-w-3xl mx-auto", deviceInfo.isMobile ? "text-base" : "text-lg")}>
            This urgent assessment helps identify symptoms of cauda equina syndrome - a surgical emergency requiring immediate medical attention
          </p>
        </div>

        {/* Emergency Warning */}
        <div className="mb-12">
          <Card className="bg-muted/50 dark:bg-muted/30 border border-border shadow-xl">
            <CardContent className="pt-8 pb-8">
              <div className="flex items-start gap-4">
                <div className="p-3 rounded-xl bg-muted/40 dark:bg-muted/30 flex-shrink-0">
                  <AlertTriangle className="h-6 w-6 text-foreground" />
                </div>
                <div>
                  <h3 className="font-bold text-foreground mb-3 text-lg">Medical Emergency Warning</h3>
                  <p className="text-base text-foreground font-medium leading-relaxed">
                    If you are experiencing sudden bladder problems, loss of bowel control, or numbness between your legs,
                    <strong> go to the emergency department immediately</strong> - do not complete this assessment.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Question Card */}
        <Card className="medical-card/95 backdrop-blur-sm border border-border/50 shadow-xl hover:shadow-3xl transition-all duration-300">
          <CardHeader className="pb-8">
            <CardTitle className="flex items-centre gap-4 text-foreground mb-4">
              <div className="p-3 rounded-xl bg-primary/10 border border-primary/20">
                <Zap className="h-6 w-6 text-primary" />
              </div>
              <span className="font-bold text-xl">Question {currentQuestion + 1} of {emergencyQuestions.length}</span>
            </CardTitle>
            <CardDescription className={cn("text-foreground font-bold leading-relaxed", deviceInfo.isMobile ? "text-lg" : "text-xl lg:text-2xl")}>
              {emergencyQuestions[currentQuestion]?.question}
            </CardDescription>
            {emergencyQuestions[currentQuestion]?.description && (
              <div className="text-foreground/80 text-base leading-relaxed mt-4 bg-primary/10 dark:bg-primary/5 border border-primary/50 p-4 rounded-xl">
                <div className="flex items-start gap-3">
                  <Brain className="h-5 w-5 text-primary mt-0.5 flex-shrink-0" />
                  <span className="font-medium">{emergencyQuestions[currentQuestion].description}</span>
                </div>
              </div>
            )}
          </CardHeader>
          <CardContent className="pt-0 pb-8">
            <RadioGroup
              value={answers[emergencyQuestions[currentQuestion]?.id] || ''}
              onValueChange={handleAnswer}
              className="space-y-5"
            >
              {emergencyQuestions[currentQuestion]?.options.map((option) => (
                <div key={option.value} className={cn(
                  "flex items-start space-x-4 p-5 rounded-xl border border-border/50 bg-card/70 backdrop-blur-sm hover:bg-card/90 hover:border-primary/40 hover:shadow-lg transition-all duration-300 hover:scale-[1.02]",
                  option.urgency === 'immediate' ? 'border-border bg-muted/30' :
                  option.urgency === 'urgent' ? 'border-info/50 bg-info-light/20' :
                  ''
                )}>
                  <RadioGroupItem value={option.value} id={option.value} className="mt-2 border-2 w-5 h-5" />
                  <Label htmlFor={option.value} className="font-semibold cursor-pointer flex-1 text-foreground hover:text-primary transition-colors duration-200 leading-relaxed">
                    {option.label}
                    {option.urgency === 'immediate' && (
                      <Badge variant="muted" className="ml-3 text-xs font-bold px-2 py-1">EMERGENCY</Badge>
                    )}
                  </Label>
                </div>
              ))}
            </RadioGroup>
          </CardContent>
        </Card>

        {/* Navigation */}
        <div className={cn("flex justify-between mt-12", deviceInfo.isMobile ? "flex-col gap-6" : "")}>
          <Button
            variant="outline"
            onClick={handlePrevious}
            disabled={currentQuestion === 0}
            className={cn("px-8 py-4 font-bold border-2 hover:shadow-xl transition-all duration-300 hover:scale-105", deviceInfo.isMobile ? "order-2" : "")}
          >
            <ArrowLeft className="mr-3 h-5 w-5" />
            Previous Question
          </Button>
          <Button
            onClick={handleNext}
            disabled={!canProceed}
            className={cn(
              deviceInfo.isMobile ? "order-1" : "",
              "bg-muted hover:bg-muted dark:bg-muted dark:hover:bg-muted px-8 py-4 font-bold shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105"
            )}
          >
            {isLastQuestion ? 'Get Emergency Assessment' : 'Next Question'}
            <ArrowRight className="ml-3 h-5 w-5" />
          </Button>
        </div>
      </div>
    </section>
  );
}

export default EmergencyAssessmentTool;
