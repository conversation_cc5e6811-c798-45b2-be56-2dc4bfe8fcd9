import { MapPin, Brain, Target, Info, ChevronDown, ChevronUp, Activity, Scissors } from 'lucide-react';
import React, { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Collapsible, CollapsibleContent } from '@/components/ui/collapsible';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface MeningiomaLocation {
  location: string;
  description: string;
  symptoms: string[];
  surgicalConsiderations: string[];
  frequency: string;
}

interface MeningiomaLocationsSectionProps {
  title: string;
  description: string;
  commonSites: MeningiomaLocation[];
}

export function MeningiomaLocationsSection({ 
  title, 
  description, 
  commonSites 
}: MeningiomaLocationsSectionProps) {
  const deviceInfo = useDeviceDetection();
  const [expandedLocation, setExpandedLocation] = useState<string | null>(null);

  const toggleExpanded = (location: string) => {
    setExpandedLocation(expandedLocation === location ? null : location);
  };

  const getLocationIcon = (location: string) => {
    if (location.includes('Convexity')) return Brain;
    if (location.includes('Parasagittal')) return Target;
    if (location.includes('Sphenoid')) return MapPin;
    if (location.includes('Posterior')) return Activity;
    return Brain;
  };

  const getLocationColor = (location: string) => {
    if (location.includes('Convexity')) return 'text-foreground bg-info-light border-info/30';
    if (location.includes('Parasagittal')) return 'text-foreground bg-success-light border-success/30';
    if (location.includes('Sphenoid')) return 'text-medical-blue bg-medical-blue-light border-medical-blue/30';
    if (location.includes('Posterior')) return 'text-foreground bg-muted-light border-border/70';
    return 'text-muted-foreground bg-muted border-border';
  };

  const getSurgicalComplexity = (considerations: string[]) => {
    const complexityIndicators = considerations.join(' ').toLowerCase();
    if (complexityIndicators.includes('complex') || complexityIndicators.includes('higher') || complexityIndicators.includes('risk')) {
      return { level: 'High Complexity', colour: 'bg-muted text-foreground' };
    }
    if (complexityIndicators.includes('accessible') || complexityIndicators.includes('good')) {
      return { level: 'Low Complexity', colour: 'bg-success-light/30 text-foreground' };
    }
    return { level: 'Moderate Complexity', colour: 'bg-info-light/30 text-foreground' };
  };

  const getFrequencyNumber = (frequency: string) => {
    const match = frequency.match(/(\d+)-?(\d+)?%/);
    if (match) {
      return match[2] ? parseInt(match[2]) : parseInt(match[1]);
    }
    return 0;
  };

  return (
    <section className={cn("py-16 bg-muted", deviceInfo.isMobile ? "px-4" : "")}>
      <div className="container">
        <div className="text-centre mb-12">
          <h2 className={cn(
            "font-bold mb-4",
            deviceInfo.isMobile ? "text-2xl" : "text-3xl"
          )}>
            {title}
          </h2>
          <p className={cn(
            "text-muted-foreground max-w-3xl mx-auto",
            deviceInfo.isMobile ? "text-sm" : "text-lg"
          )}>
            {description}
          </p>
        </div>

        {/* Location Overview */}
        <div className="mb-12">
          <Card className="bg-muted">
            <CardContent className="pt-6">
              <div className={cn(
                "grid gap-8 items-center",
                deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2"
              )}>
                <div>
                  <h3 className="text-xl font-semibold mb-4">Meningioma Distribution</h3>
                  <p className="text-muted-foreground mb-4">
                    Meningiomas can occur anywhere along the meninges, but certain locations are more common. 
                    The location significantly influences symptoms, surgical approach, and treatment outcomes. 
                    Understanding these patterns helps in diagnosis and treatment planning.
                  </p>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-info rounded-full"></div>
                      <span className="text-sm">Convexity (25-30%)</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-success rounded-full"></div>
                      <span className="text-sm">Parasagittal (20-25%)</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-medical-blue rounded-full"></div>
                      <span className="text-sm">Sphenoid Wing (15-20%)</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 bg-muted rounded-full"></div>
                      <span className="text-sm">Posterior Fossa (10-15%)</span>
                    </div>
                  </div>
                </div>
                <div className="flex justify-center">
                  <div className="relative">
                    <img
                      src="/images/brain-conditions/brain-anatomy-detailed.jpg"
                      alt="Meningioma locations diagram showing common sites"
                      className="rounded-lg shadow-lg max-w-full h-auto"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-lg"></div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Location Details */}
        <div className="space-y-6">
          {commonSites.map((site, index) => {
            const Icon = getLocationIcon(site.location);
            const isExpanded = expandedLocation === site.location;
            const surgicalComplexity = getSurgicalComplexity(site.surgicalConsiderations);
            
            return (
              <Card key={index} className={cn("transition-all duration-200", getLocationColor(site.location))}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="p-2 rounded-full bg-background/80">
                        <Icon className="h-5 w-5" />
                      </div>
                      <div>
                        <CardTitle className="text-lg">{site.location}</CardTitle>
                        <CardDescription className="text-sm">{site.description}</CardDescription>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary" className="bg-background/80">
                        {site.frequency}
                      </Badge>
                      <Badge className={surgicalComplexity.colour}>
                        {surgicalComplexity.level}
                      </Badge>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => toggleExpanded(site.location)}
                        className="flex items-centre gap-1"
                      >
                        <Info className="h-4 w-4" />
                        {isExpanded ? (
                          <ChevronUp className="h-4 w-4" />
                        ) : (
                          <ChevronDown className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                
                <Collapsible open={isExpanded}>
                  <CollapsibleContent>
                    <CardContent className="pt-0">
                      <div className={cn(
                        "grid gap-6",
                        deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2"
                      )}>
                        {/* Common Symptoms */}
                        <div>
                          <h4 className="font-semibold mb-3 flex items-center gap-2">
                            <Brain className="h-4 w-4 text-info" />
                            Common Symptoms
                          </h4>
                          <ul className="space-y-2">
                            {site.symptoms.map((symptom, idx) => (
                              <li key={idx} className="flex items-start gap-2">
                                <div className="w-2 h-2 bg-current rounded-full mt-2 flex-shrink-0 opacity-60" />
                                <span className="text-sm">{symptom}</span>
                              </li>
                            ))}
                          </ul>
                        </div>

                        {/* Surgical Considerations */}
                        <div>
                          <h4 className="font-semibold mb-3 flex items-centre gap-2">
                            <Scissors className="h-4 w-4 text-info" />
                            Surgical Considerations
                          </h4>
                          <ul className="space-y-2">
                            {site.surgicalConsiderations.map((consideration, idx) => (
                              <li key={idx} className="flex items-start gap-2">
                                <div className="w-2 h-2 bg-current rounded-full mt-2 flex-shrink-0 opacity-60" />
                                <span className="text-sm">{consideration}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>

                      {/* Frequency Visualization */}
                      <div className="mt-6 p-4 bg-background/50 rounded-lg">
                        <h4 className="font-semibold mb-2">Frequency Distribution</h4>
                        <div className="space-y-2">
                          <div className="flex justify-between items-center">
                            <span className="text-sm">Relative Frequency</span>
                            <span className="text-sm font-medium">{site.frequency}</span>
                          </div>
                          <div className="w-full bg-muted rounded-full h-2">
                            <div 
                              className="bg-current h-2 rounded-full opacity-60" 
                              style={{ width: `${getFrequencyNumber(site.frequency)}%` }}
                            ></div>
                          </div>
                        </div>
                      </div>

                      {/* Treatment Approach */}
                      <div className="mt-4 p-4 bg-background/50 rounded-lg">
                        <h4 className="font-semibold mb-2">Treatment Approach</h4>
                        <p className="text-sm text-muted-foreground">
                          {site.location.includes('Convexity') && 
                            "Convexity meningiomas are often ideal candidates for surgical resection due to their accessible location and generally good outcomes. Complete resection is usually achievable with low morbidity."
                          }
                          {site.location.includes('Parasagittal') && 
                            "Parasagittal meningiomas require careful consideration of venous drainage patterns. Surgical planning must account for potential venous reconstruction and risk of postoperative complications."
                          }
                          {site.location.includes('Sphenoid') && 
                            "Sphenoid wing meningiomas often require skull base approaches and careful preservation of cranial nerves. Subtotal resection followed by radiation therapy may be appropriate for complex cases."
                          }
                          {site.location.includes('Posterior') && 
                            "Posterior fossa meningiomas require expertise in skull base surgery with careful attention to brainstem and cranial nerve preservation. Staged procedures may be necessary for large tumours."
                          }
                        </p>
                      </div>
                    </CardContent>
                  </CollapsibleContent>
                </Collapsible>
              </Card>
            );
          })}
        </div>

        {/* Location-Based Treatment Summary */}
        <div className="mt-12">
          <Card className="bg-muted">
            <CardHeader>
              <CardTitle className="flex items-centre gap-2">
                <MapPin className="h-5 w-5 text-foreground" />
                Location-Based Treatment Planning
              </CardTitle>
              <CardDescription>
                How tumour location influences treatment decisions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className={cn(
                "grid gap-6",
                deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2"
              )}>
                <div>
                  <h4 className="font-semibold mb-3 text-success">Favourable Locations for Surgery</h4>
                  <ul className="space-y-2">
                    <li className="flex items-start gap-2">
                      <div className="w-2 h-2 bg-success rounded-full mt-2 flex-shrink-0" />
                      <span className="text-sm">Convexity meningiomas - accessible and low risk</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <div className="w-2 h-2 bg-success rounded-full mt-2 flex-shrink-0" />
                      <span className="text-sm">Anterior falx meningiomas - good surgical access</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <div className="w-2 h-2 bg-success rounded-full mt-2 flex-shrink-0" />
                      <span className="text-sm">Lateral sphenoid wing - often resectable</span>
                    </li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold mb-3 text-info">Challenging Locations</h4>
                  <ul className="space-y-2">
                    <li className="flex items-start gap-2">
                      <div className="w-2 h-2 bg-info rounded-full mt-2 flex-shrink-0" />
                      <span className="text-sm">Skull base locations - complex anatomy</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <div className="w-2 h-2 bg-info rounded-full mt-2 flex-shrink-0" />
                      <span className="text-sm">Cavernous sinus - high cranial nerve risk</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <div className="w-2 h-2 bg-info rounded-full mt-2 flex-shrink-0" />
                      <span className="text-sm">Foramen magnum - brainstem proximity</span>
                    </li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Clinical Significance */}
        <div className="mt-12">
          <Card className="bg-info-light border-info/30">
            <CardHeader>
              <CardTitle className="flex items-centre gap-2 text-info">
                <Info className="h-5 w-5" />
                Clinical Significance
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-info text-sm">
                The location of a meningioma is one of the most important factors in determining treatment approach and prognosis. 
                Accessible locations allow for complete surgical resection with excellent outcomes, while skull base locations 
                may require multimodal treatment approaches. Understanding location-specific challenges helps patients make 
                informed decisions about their care.
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
}

export default MeningiomaLocationsSection;
