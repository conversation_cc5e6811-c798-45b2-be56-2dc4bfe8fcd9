
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Inter:wght@300;400;500;600&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Premium FAQ Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.1);
  }
  50% {
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.2);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Custom utility classes */
.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-slide-in-left {
  animation: slideInLeft 0.6s ease-out;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* Enhanced scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgb(241 245 249);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, rgb(59 130 246), rgb(99 102 241));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, rgb(37 99 235), rgb(79 70 229));
}

@layer base {
  :root {
    /* Enhanced Light Theme with Better Contrast - WCAG AA Compliant */
    --background: 0 0% 100%;
    --foreground: 210 40% 8%;

    --card: 0 0% 100%;
    --card-foreground: 210 40% 8%;

    --popover: 0 0% 100%;
    --popover-foreground: 210 40% 8%;

    --primary: 210 100% 45%;
    --primary-foreground: 0 0% 100%;

    --secondary: 210 40% 96%;
    --secondary-foreground: 210 40% 15%;

    --muted: 210 40% 94%;
    --muted-foreground: 210 30% 25%;

    --accent: 210 40% 96%;
    --accent-foreground: 210 40% 15%;

    --destructive: 0 50% 45%;
    --destructive-foreground: 0 0% 100%;

    --border: 210 20% 85%;
    --input: 210 20% 85%;
    --ring: 210 100% 45%;

    --radius: 0.5rem;

    /* Professional Color Palette - Enhanced Contrast */
    --medical-blue: 210 100% 45%;
    --medical-blue-foreground: 0 0% 100%;
    --medical-blue-light: 210 100% 97%;
    --medical-blue-dark: 210 100% 35%;

    --success: 142 76% 30%;
    --success-foreground: 0 0% 100%;
    --success-light: 142 76% 97%;

    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 100%;
    --warning-light: 38 92% 97%;

    --error: 0 85% 55%;
    --error-foreground: 0 0% 100%;
    --error-light: 0 85% 97%;

    --info: 199 89% 40%;
    --info-foreground: 0 0% 100%;
    --info-light: 199 89% 97%;
  }

  .dark {
    /* Professional Dark Theme - Clean Blue-Gray Palette */
    --background: 220 30% 8%;
    --foreground: 220 15% 95%;

    --card: 220 25% 12%;
    --card-foreground: 220 15% 95%;

    --popover: 220 25% 12%;
    --popover-foreground: 220 15% 95%;

    --primary: 210 100% 65%;
    --primary-foreground: 220 30% 8%;

    --secondary: 220 20% 18%;
    --secondary-foreground: 220 15% 90%;

    --muted: 220 20% 18%;
    --muted-foreground: 220 15% 70%;

    --accent: 220 20% 18%;
    --accent-foreground: 220 15% 90%;

    --destructive: 0 45% 50%;
    --destructive-foreground: 220 15% 95%;

    --border: 220 20% 22%;
    --input: 220 20% 22%;
    --ring: 210 100% 65%;

    /* Professional Dark Color Palette - Clean Medical Colors */
    --medical-blue: 210 100% 65%;
    --medical-blue-foreground: 220 30% 8%;
    --medical-blue-light: 210 100% 15%;
    --medical-blue-dark: 210 100% 75%;

    --success: 142 76% 55%;
    --success-foreground: 220 30% 8%;
    --success-light: 142 76% 15%;

    --warning: 38 92% 60%;
    --warning-foreground: 220 30% 8%;
    --warning-light: 38 92% 15%;

    --error: 0 85% 70%;
    --error-foreground: 220 30% 8%;
    --error-light: 0 85% 15%;

    --info: 199 89% 65%;
    --info-foreground: 220 30% 8%;
    --info-light: 199 89% 15%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  html, body {
    margin: 0;
    padding: 0;
    border: none;
    outline: none;
  }

  body {
    @apply bg-background text-foreground antialiased;
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    font-weight: 400;
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: 'Playfair Display', serif;
    font-weight: 600;
    line-height: 1.2;
    color: hsl(var(--foreground));
  }

  h1 {
    @apply text-4xl md:text-5xl lg:text-6xl font-bold;
  }

  h2 {
    @apply text-3xl md:text-4xl lg:text-5xl font-semibold;
  }

  h3 {
    @apply text-2xl md:text-3xl lg:text-4xl font-semibold;
  }

  h4 {
    @apply text-xl md:text-2xl lg:text-3xl font-medium;
  }

  h5 {
    @apply text-lg md:text-xl lg:text-2xl font-medium;
  }

  h6 {
    @apply text-base md:text-lg lg:text-xl font-medium;
  }

  p {
    @apply text-foreground/90 leading-relaxed;
  }

  strong, b {
    @apply font-semibold text-foreground;
  }

  em, i {
    @apply italic text-foreground/95;
  }

  /* Remove any browser default progress bars or loading indicators */
  ::-webkit-progress-bar,
  ::-webkit-progress-value,
  progress {
    display: none !important;
  }
}

@layer components {
  .container {
    @apply px-4 md:px-6 lg:px-8 mx-auto;
  }

  .btn-primary {
    @apply bg-primary text-primary-foreground rounded-full px-6 py-2.5 font-medium transition-all duration-300 hover:shadow-lg hover:shadow-primary/20 active:scale-[0.98];
  }

  .section {
    @apply py-12 md:py-16 lg:py-24;
  }

  /* Enhanced Glass Card with Better Contrast */
  .glass-card {
    @apply bg-card/95 backdrop-blur-lg border border-border/50 rounded-xl shadow-lg;
  }

  /* Professional Card Styling */
  .medical-card {
    @apply bg-card border border-border/50 rounded-xl shadow-md hover:shadow-lg transition-all duration-300;
  }

  .medical-card-content {
    @apply text-card-foreground;
  }

  .medical-card-inner {
    @apply bg-card/50 border border-border/30;
  }

  /* Enhanced Card Variants */
  .medical-card-elevated {
    @apply medical-card shadow-lg hover:shadow-xl border-border/60;
  }

  .medical-card-interactive {
    @apply medical-card hover:scale-105 hover:border-primary/30 cursor-pointer;
  }

  .medical-card-feature {
    @apply medical-card bg-primary/5 border-primary/20 hover:bg-primary/10;
  }

  /* Professional Section Backgrounds - Standardized */
  .section-background {
    @apply bg-background border-y border-border/20;
  }

  .section-background-muted {
    @apply bg-muted/30 border-y border-border/20;
  }

  .section-background-alt {
    @apply bg-accent/30 border-y border-border/20;
  }

  .section-background-card {
    @apply bg-card border-y border-border/20;
  }

  /* Professional Section Spacing */
  .section-spacing {
    @apply py-12 md:py-16 lg:py-20;
  }

  .section-spacing-sm {
    @apply py-8 md:py-12 lg:py-16;
  }

  .section-spacing-lg {
    @apply py-16 md:py-20 lg:py-24;
  }

  /* Container Standardization */
  .section-container {
    @apply container mx-auto px-4 md:px-6 lg:px-8;
  }

  .section-container-narrow {
    @apply max-w-4xl mx-auto px-4 md:px-6 lg:px-8;
  }

  .section-container-wide {
    @apply max-w-7xl mx-auto px-4 md:px-6 lg:px-8;
  }

  /* Mobile-optimized components */
  .mobile-container {
    @apply px-mobile-md sm:px-mobile-lg md:px-mobile-xl lg:px-mobile-2xl mx-auto;
  }

  .mobile-section {
    @apply py-mobile-xl sm:py-mobile-2xl md:py-12 lg:py-16;
  }

  /* Professional Badge Styles with Enhanced Contrast */
  .badge-emergency {
    @apply bg-muted-light text-foreground border border-border/70 font-semibold shadow-sm;
  }

  .badge-info {
    @apply bg-info-light text-info border border-info/30 font-semibold shadow-sm;
  }

  .badge-routine {
    @apply bg-success-light text-success border border-success/30 font-semibold shadow-sm;
  }

  .badge-medical {
    @apply bg-medical-blue-light text-medical-blue border border-medical-blue/30 font-semibold shadow-sm;
  }

  /* Color Utility Classes */
  .bg-info-light {
    background-color: hsl(var(--info-light));
  }

  .bg-success-light {
    background-color: hsl(var(--success-light));
  }

  .bg-muted-light {
    background-color: hsl(var(--muted));
  }

  .bg-medical-blue-light {
    background-color: hsl(var(--medical-blue-light));
  }

  .text-info {
    color: hsl(var(--info));
  }

  .text-success {
    color: hsl(var(--success));
  }

  .text-foreground {
    color: hsl(var(--foreground));
  }

  .text-medical-blue {
    color: hsl(var(--medical-blue));
  }

  .text-medical-blue-foreground {
    color: hsl(var(--medical-blue-foreground));
  }

  .border-info\/30 {
    border-color: hsl(var(--info) / 0.3);
  }

  .border-success\/30 {
    border-color: hsl(var(--success) / 0.3);
  }

  .border-border\/30 {
    border-color: hsl(var(--border) / 0.3);
  }

  .border-medical-blue\/30 {
    border-color: hsl(var(--medical-blue) / 0.3);
  }

  /* Additional Theme Color Classes */
  .bg-info {
    background-color: hsl(var(--info));
  }

  .bg-success {
    background-color: hsl(var(--success));
  }

  .bg-muted {
    background-color: hsl(var(--muted));
  }

  .bg-medical-blue {
    background-color: hsl(var(--medical-blue));
  }

  .border-info {
    border-color: hsl(var(--info));
  }

  .border-success {
    border-color: hsl(var(--success));
  }

  .border-border {
    border-color: hsl(var(--border));
  }

  .border-medical-blue {
    border-color: hsl(var(--medical-blue));
  }

  .border-background {
    border-color: hsl(var(--background));
  }

  .border-foreground {
    border-color: hsl(var(--foreground));
  }

  /* Emergency styling */
  .border-emergency {
    @apply border-border;
  }

  .border-emergency\/30 {
    @apply border-border/70;
  }

  .bg-emergency-light {
    @apply bg-muted-light;
  }

  .text-emergency {
    @apply text-foreground;
  }

  /* Enhanced Text Readability with Clear Visual Hierarchy */
  .text-enhanced {
    @apply text-foreground font-medium leading-relaxed;
  }

  .text-enhanced-muted {
    @apply text-muted-foreground leading-relaxed font-normal;
  }

  .text-enhanced-strong {
    @apply text-foreground font-semibold leading-tight;
  }

  .text-enhanced-light {
    @apply text-foreground/75 font-normal leading-relaxed;
  }

  .text-enhanced-heading {
    @apply text-foreground font-bold leading-tight tracking-tight;
  }

  .text-enhanced-subheading {
    @apply text-foreground/95 font-semibold leading-snug tracking-wide;
  }

  .text-enhanced-body {
    @apply text-foreground/85 font-normal leading-relaxed;
  }

  .text-enhanced-caption {
    @apply text-primary font-semibold leading-normal text-sm uppercase tracking-wider;
  }

  .text-enhanced-link {
    @apply text-primary font-semibold hover:text-primary/80 transition-colors duration-200;
  }

  .text-enhanced-muted {
    @apply text-muted-foreground font-normal leading-relaxed;
  }

  .text-enhanced-strong {
    @apply text-foreground font-bold leading-tight;
  }

  /* Professional Text Alignment Classes */
  .text-center-aligned {
    @apply text-center items-center justify-center;
  }

  .text-left-aligned {
    @apply text-left items-start justify-start;
  }

  .text-right-aligned {
    @apply text-right items-end justify-end;
  }

  /* Consistent Icon Alignment */
  .icon-text-aligned {
    @apply flex items-center gap-2;
  }

  .icon-text-aligned-vertical {
    @apply flex flex-col items-center gap-2;
  }

  /* Professional Spacing Patterns */
  .content-spacing-sm {
    @apply space-y-4 md:space-y-6;
  }

  .content-spacing-md {
    @apply space-y-6 md:space-y-8;
  }

  .content-spacing-lg {
    @apply space-y-8 md:space-y-12;
  }

  /* Additional Enhanced Text Classes */
  .text-enhanced-small {
    @apply text-foreground/80 text-sm font-medium leading-relaxed;
  }

  .text-enhanced-large {
    @apply text-foreground text-lg font-medium leading-relaxed;
  }

  .link-enhanced-subtle {
    @apply text-muted-foreground hover:text-foreground transition-colors duration-200;
  }

  /* Professional Card Enhancements */
  .card-enhanced {
    @apply bg-card border border-border/50 rounded-lg shadow-sm hover:shadow-md transition-all duration-300;
  }

  .card-enhanced-interactive {
    @apply bg-card border border-border/50 rounded-lg shadow-sm hover:shadow-lg hover:border-border transition-all duration-300 cursor-pointer;
  }

  /* Technology Section Specific Styling */
  .technology-card {
    @apply medical-card p-8 rounded-xl shadow-md hover:shadow-xl transition-all duration-300 hover:scale-105;
    @apply border border-border/30 hover:border-primary/20;
  }

  .technology-icon-container {
    @apply rounded-2xl flex items-center justify-center shadow-lg backdrop-blur-sm;
    @apply transition-all duration-300 hover:shadow-xl hover:scale-110;
  }

  /* Professional Animation Classes */
  .animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out forwards;
  }

  .animate-scale-in {
    animation: scaleIn 0.4s ease-out forwards;
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes scaleIn {
    from {
      opacity: 0;
      transform: scale(0.9);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  /* Professional Button Enhancements */
  .button-enhanced {
    @apply bg-primary text-primary-foreground hover:bg-primary/90 transition-all duration-200 font-medium;
  }

  .button-enhanced-outline {
    @apply border border-border text-foreground hover:bg-muted/50 transition-all duration-200;
  }

  /* Professional Section Styling */
  .section-enhanced {
    @apply py-16 md:py-20 lg:py-24 bg-background border-y border-border/20;
  }

  .section-enhanced-alt {
    @apply py-16 md:py-20 lg:py-24 bg-muted/30 border-y border-border/20;
  }

  .section-enhanced-primary {
    @apply py-16 md:py-20 lg:py-24 bg-primary/5 border-y border-primary/20;
  }

  /* Enhanced Visual Hierarchy */
  .visual-hierarchy-section {
    @apply space-y-8 md:space-y-12 lg:space-y-16;
  }

  .visual-hierarchy-content {
    @apply space-y-6 md:space-y-8;
  }

  /* Enhanced Grid Layouts */
  .grid-technology {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 lg:gap-8;
  }

  .grid-features {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8;
  }

  .grid-services {
    @apply grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 lg:gap-8;
  }

  /* Professional Spacing Utilities */
  .spacing-technology {
    @apply space-y-8 md:space-y-12 lg:space-y-16;
  }

  .spacing-content {
    @apply space-y-6 md:space-y-8 lg:space-y-10;
  }

  .spacing-section {
    @apply space-y-12 md:space-y-16 lg:space-y-20;
  }

  /* Enhanced Color Utilities */
  .enhanced-border {
    @apply border-border/50;
  }

  .enhanced-hover {
    @apply hover:bg-accent/50;
  }

  .enhanced-accent {
    @apply text-primary;
  }

  .bg-enhanced-accent {
    @apply bg-primary/10;
  }



  .text-enhanced-label {
    @apply text-foreground font-medium leading-none text-sm tracking-wide;
  }

  /* Visual Hierarchy Spacing */
  .section-spacing {
    @apply py-16 md:py-20 lg:py-24;
  }

  .section-spacing-sm {
    @apply py-12 md:py-16 lg:py-20;
  }

  .content-spacing {
    @apply space-y-6 md:space-y-8;
  }

  /* Enhanced Card Hierarchy */
  .card-primary {
    @apply medical-card border-primary/20 shadow-lg hover:shadow-xl;
  }

  .card-secondary {
    @apply medical-card border-border/30 shadow-md hover:shadow-lg;
  }

  .card-accent {
    @apply medical-card bg-accent/50 border-accent/30 shadow-sm hover:shadow-md;
  }

  .link-enhanced-subtle {
    @apply text-foreground/80 hover:text-primary transition-colors duration-200;
  }

  /* Professional Button Styling */
  .btn-enhanced {
    @apply px-6 py-3 rounded-lg font-semibold transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }

  .btn-primary {
    @apply btn-enhanced bg-primary text-primary-foreground hover:bg-primary/90 hover:scale-105 focus:ring-primary shadow-md hover:shadow-lg;
  }

  .btn-secondary {
    @apply btn-enhanced bg-secondary text-secondary-foreground hover:bg-secondary/80 hover:scale-105 focus:ring-secondary shadow-md hover:shadow-lg;
  }

  .btn-outline {
    @apply btn-enhanced border-2 border-primary text-primary hover:bg-primary hover:text-primary-foreground hover:scale-105 focus:ring-primary;
  }

  .btn-ghost {
    @apply btn-enhanced text-primary hover:bg-primary/10 hover:scale-105 focus:ring-primary;
  }

  /* Enhanced Badge Styling */
  .badge-enhanced {
    @apply inline-flex items-center px-3 py-1 rounded-full text-sm font-medium;
  }

  .badge-primary {
    @apply badge-enhanced bg-primary/10 text-primary border border-primary/20;
  }

  .badge-secondary {
    @apply badge-enhanced bg-secondary/10 text-secondary-foreground border border-secondary/20;
  }

  .badge-success {
    @apply badge-enhanced bg-green-100 text-green-800 border border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800;
  }

  .badge-warning {
    @apply badge-enhanced bg-yellow-100 text-yellow-800 border border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800;
  }
}

  .mobile-card {
    @apply bg-card rounded-lg shadow-sm border border-border/50 p-mobile-lg transition-all duration-200;
  }

  .mobile-button {
    @apply min-h-[44px] min-w-[44px] touch-manipulation select-none transition-all duration-200 active:scale-95;
  }

  .mobile-input {
    @apply min-h-[44px] text-base touch-manipulation;
  }

  .mobile-text {
    @apply text-mobile-base leading-relaxed;
  }

  .mobile-heading {
    @apply text-mobile-2xl font-bold leading-tight;
  }

  .mobile-subheading {
    @apply text-mobile-lg font-semibold leading-snug;
  }

  /* Touch-friendly utilities */
  .touch-target {
    @apply min-h-[44px] min-w-[44px] flex items-center justify-center;
  }

  .touch-feedback {
    @apply transition-transform duration-100 active:scale-95 touch-manipulation;
  }

  .swipe-container {
    @apply overflow-x-auto scrollbar-hide touch-pan-x;
  }

  .wave-animation {
    animation: wave 12s linear infinite;
    animation-delay: -2s;
    transform-origin: center bottom;
  }

  .page-transition-enter {
    opacity: 0;
    transform: translateY(10px);
  }

  .page-transition-enter-active {
    opacity: 1;
    transform: translateY(0);
    transition: opacity 400ms, transform 400ms;
  }

  .page-transition-exit {
    opacity: 1;
  }

  .page-transition-exit-active {
    opacity: 0;
    transition: opacity 300ms;
  }

  /* Mobile-specific utilities */
  .mobile-safe-area {
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }

  .mobile-scroll-smooth {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
  }

  .mobile-no-scroll {
    overflow: hidden;
    position: fixed;
    width: 100%;
  }

  .mobile-backdrop {
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
  }

  /* Hide scrollbars on mobile */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Touch-specific styles */
  @media (hover: none) and (pointer: coarse) {
    .hover-only {
      display: none;
    }

    .touch-only {
      display: block;
    }

    /* Larger touch targets on touch devices */
    button, a, input, select, textarea {
      min-height: 44px;
    }

    /* Remove hover effects on touch devices */
    .hover\:scale-105:hover {
      transform: none;
    }

    .hover\:shadow-lg:hover {
      box-shadow: none;
    }
  }

  @media (hover: hover) and (pointer: fine) {
    .touch-only {
      display: none;
    }

    .hover-only {
      display: block;
    }
  }

  /* Mobile typography improvements */
  @media (max-width: 640px) {
    h1 {
      font-size: 2rem;
      line-height: 2.25rem;
      word-wrap: break-word;
      hyphens: auto;
    }

    h2 {
      font-size: 1.75rem;
      line-height: 2rem;
    }

    h3 {
      font-size: 1.5rem;
      line-height: 1.75rem;
    }

    h4 {
      font-size: 1.25rem;
      line-height: 1.5rem;
    }

    p {
      font-size: 0.95rem;
      line-height: 1.5rem;
      word-wrap: break-word;
    }
  }

  /* Responsive text overflow prevention */
  .text-responsive {
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
  }

  /* Breadcrumb responsive improvements */
  .breadcrumb-responsive {
    word-break: break-word;
    overflow-wrap: break-word;
  }

  /* Accessibility Enhancements */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  .skip-link {
    position: absolute;
    top: -100px;
    left: 6px;
    background: hsl(var(--primary));
    color: hsl(var(--primary-foreground));
    padding: 8px 16px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 1000;
    transition: top 0.3s;
    opacity: 0;
    pointer-events: none;
  }

  .skip-link:focus {
    top: 6px;
    opacity: 1;
    pointer-events: auto;
  }

  /* Focus management */
  .keyboard-navigation *:focus {
    outline: 2px solid hsl(var(--ring));
    outline-offset: 2px;
  }

  .keyboard-navigation button:focus,
  .keyboard-navigation a:focus,
  .keyboard-navigation input:focus,
  .keyboard-navigation select:focus,
  .keyboard-navigation textarea:focus {
    outline: 2px solid hsl(var(--ring));
    outline-offset: 2px;
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    :root {
      --border: 0 0% 0%;
      --input: 0 0% 0%;
    }

    .dark {
      --border: 0 0% 100%;
      --input: 0 0% 100%;
    }
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      scroll-behavior: auto !important;
    }

    .wave-animation {
      animation: none;
    }
  }

  /* Performance optimizations */
  .will-change-transform {
    will-change: transform;
  }

  .will-change-opacity {
    will-change: opacity;
  }

  .gpu-accelerated {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
  }

  /* Loading states */
  .loading-skeleton {
    background: linear-gradient(90deg,
      hsl(var(--muted)) 25%,
      hsl(var(--muted-foreground) / 0.1) 50%,
      hsl(var(--muted)) 75%);
    background-size: 200% 100%;
    animation: loading-shimmer 2s infinite;
  }

  @keyframes loading-shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }

  /* Print styles */
  @media print {
    .no-print {
      display: none !important;
    }

    body {
      background: white !important;
      color: black !important;
    }

    a[href]:after {
      content: " (" attr(href) ")";
    }

    .page-break {
      page-break-before: always;
    }
  }
