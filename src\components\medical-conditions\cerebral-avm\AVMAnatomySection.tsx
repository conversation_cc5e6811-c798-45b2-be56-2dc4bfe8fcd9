import { Brain, Network, Droplets, Info, ChevronDown, ChevronUp, Activity, Hexagon } from 'lucide-react';
import React, { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Collapsible, CollapsibleContent } from '@/components/ui/collapsible';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface VascularComponent {
  component: string;
  description: string;
  normalFunction: string[];
  avmCharacteristics: string[];
}

interface AVMAnatomySectionProps {
  title: string;
  description: string;
  vascularComponents: VascularComponent[];
}

export function AVMAnatomySection({ 
  title, 
  description, 
  vascularComponents 
}: AVMAnatomySectionProps) {
  const deviceInfo = useDeviceDetection();
  const [expandedComponent, setExpandedComponent] = useState<string | null>(null);

  const toggleExpanded = (component: string) => {
    setExpandedComponent(expandedComponent === component ? null : component);
  };

  const getComponentIcon = (component: string) => {
    if (component.includes('Normal')) return Brain;
    if (component.includes('Nidus')) return Hexagon;
    if (component.includes('Feeding')) return Network;
    return Activity;
  };

  const getComponentColor = (component: string) => {
    if (component.includes('Normal')) return 'text-foreground bg-info-light border-info/30';
    if (component.includes('Nidus')) return 'text-foreground bg-muted-light border-border/70';
    if (component.includes('Feeding')) return 'text-medical-blue bg-medical-blue-light border-medical-blue/30';
    return 'text-muted-foreground bg-muted border-border';
  };

  const getAbnormalityLevel = (component: string) => {
    if (component.includes('Normal')) {
      return { level: 'Normal Structure', colour: 'badge-routine' };
    }
    if (component.includes('Nidus')) {
      return { level: 'Abnormal Nidus', colour: 'badge-emergency' };
    }
    return { level: 'Altered Vessels', colour: 'badge-info' };
  };

  return (
    <section className={cn("py-16", deviceInfo.isMobile ? "px-4" : "")}>
      <div className="container">
        <div className="text-centre mb-12">
          <h2 className={cn(
            "font-bold mb-4",
            deviceInfo.isMobile ? "text-2xl" : "text-3xl"
          )}>
            {title}
          </h2>
          <p className={cn(
            "text-muted-foreground max-w-3xl mx-auto",
            deviceInfo.isMobile ? "text-sm" : "text-lg"
          )}>
            {description}
          </p>
        </div>

        {/* Anatomical Overview */}
        <div className="mb-12">
          <Card className="bg-muted">
            <CardContent className="pt-6">
              <div className={cn(
                "grid gap-8 items-centre",
                deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2"
              )}>
                <div>
                  <h3 className="text-xl font-semibold mb-4">AVM vs Normal Brain Circulation</h3>
                  <p className="text-muted-foreground mb-4">
                    AVMs are abnormal tangles of blood vessels where arteries connect directly to veins, 
                    bypassing the normal capillary bed. This creates a high-flow, low-resistance pathway 
                    that can lead to bleeding and steal blood from normal brain tissue.
                  </p>
                  <div className="space-y-2">
                    <div className="flex items-centre gap-2">
                      <div className="w-3 h-3 bg-info rounded-full"></div>
                      <span className="text-sm">Normal Brain Circulation</span>
                    </div>
                    <div className="flex items-centre gap-2">
                      <div className="w-3 h-3 bg-muted rounded-full"></div>
                      <span className="text-sm">AVM Nidus (Abnormal Tangle)</span>
                    </div>
                    <div className="flex items-centre gap-2">
                      <div className="w-3 h-3 bg-medical-blue rounded-full"></div>
                      <span className="text-sm">Feeding Arteries</span>
                    </div>
                  </div>
                </div>
                <div className="flex justify-centre">
                  <div className="relative">
                    <img 
                      src="/images/brain-conditions/brain-anatomy-detailed.jpg" 
                      alt="AVM anatomy diagram showing abnormal vascular connections"
                      className="rounded-lg shadow-lg max-w-full h-auto"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-lg"></div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Vascular Component Details */}
        <div className="space-y-6">
          {vascularComponents.map((component, index) => {
            const Icon = getComponentIcon(component.component);
            const isExpanded = expandedComponent === component.component;
            const abnormalityLevel = getAbnormalityLevel(component.component);
            
            return (
              <Card key={index} className={cn("transition-all duration-200", getComponentColor(component.component))}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="p-2 rounded-full medical-card/80 border border-border/50">
                        <Icon className="h-5 w-5" />
                      </div>
                      <div>
                        <CardTitle className="text-lg">{component.component}</CardTitle>
                        <CardDescription className="text-sm">{component.description}</CardDescription>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge className={abnormalityLevel.colour}>
                        {abnormalityLevel.level}
                      </Badge>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => toggleExpanded(component.component)}
                        className="flex items-center gap-1"
                      >
                        <Info className="h-4 w-4" />
                        {isExpanded ? (
                          <ChevronUp className="h-4 w-4" />
                        ) : (
                          <ChevronDown className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                
                <Collapsible open={isExpanded}>
                  <CollapsibleContent>
                    <CardContent className="pt-0">
                      <div className={cn(
                        "grid gap-6",
                        deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2"
                      )}>
                        {/* Normal Functions */}
                        <div>
                          <h4 className="font-semibold mb-3">Normal Functions</h4>
                          <ul className="space-y-2">
                            {component.normalFunction.map((func, idx) => (
                              <li key={idx} className="flex items-start gap-2">
                                <div className="w-2 h-2 bg-current rounded-full mt-2 flex-shrink-0 opacity-60" />
                                <span className="text-sm">{func}</span>
                              </li>
                            ))}
                          </ul>
                        </div>

                        {/* AVM Characteristics */}
                        <div>
                          <h4 className="font-semibold mb-3">AVM-Related Features</h4>
                          <ul className="space-y-2">
                            {component.avmCharacteristics.map((characteristic, idx) => (
                              <li key={idx} className="flex items-start gap-2">
                                <div className="w-2 h-2 bg-current rounded-full mt-2 flex-shrink-0 opacity-60" />
                                <span className="text-sm">{characteristic}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>

                      {/* Additional Information */}
                      <div className="mt-6 p-4 bg-background/50 rounded-lg">
                        <h4 className="font-semibold mb-2">Clinical Significance</h4>
                        <p className="text-sm text-muted-foreground">
                          {component.component.includes('Normal') && 
                            "Normal brain circulation includes arteries that branch into capillaries for oxygen exchange, then drain into veins. This system maintains appropriate pressure and ensures efficient brain perfusion."
                          }
                          {component.component.includes('Nidus') && 
                            "The AVM nidus is the abnormal tangle of vessels where arteries connect directly to veins. This high-flow, low-resistance pathway is prone to bleeding and can steal blood from normal brain tissue."
                          }
                          {component.component.includes('Feeding') && 
                            "Feeding arteries supply the AVM and often become enlarged due to high flow. They may develop aneurysms and can steal blood from normal brain tissue, causing neurological symptoms."
                          }
                        </p>
                      </div>
                    </CardContent>
                  </CollapsibleContent>
                </Collapsible>
              </Card>
            );
          })}
        </div>

        {/* AVM Formation Process */}
        <div className="mt-12">
          <Card className="bg-muted">
            <CardHeader>
              <CardTitle className="flex items-centre gap-2">
                <Droplets className="h-5 w-5 text-foreground" />
                How AVMs Develop and Cause Problems
              </CardTitle>
              <CardDescription>
                Understanding AVM development and complications
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className={cn(
                "grid gap-6",
                deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-3"
              )}>
                <div className="text-centre">
                  <div className="w-12 h-12 bg-muted text-primary-foreground rounded-full flex items-centre justify-centre font-bold mx-auto mb-3">
                    1
                  </div>
                  <h4 className="font-semibold mb-2">Embryonic Development</h4>
                  <p className="text-sm text-muted-foreground">
                    Abnormal vascular development during embryonic life creates direct arterial-venous connections
                  </p>
                </div>
                <div className="text-centre">
                  <div className="w-12 h-12 bg-muted text-primary-foreground rounded-full flex items-centre justify-centre font-bold mx-auto mb-3">
                    2
                  </div>
                  <h4 className="font-semibold mb-2">High-Flow Pathway</h4>
                  <p className="text-sm text-muted-foreground">
                    Direct arterial-venous connection creates high-flow, low-resistance pathway
                  </p>
                </div>
                <div className="text-centre">
                  <div className="w-12 h-12 bg-muted text-primary-foreground rounded-full flex items-centre justify-centre font-bold mx-auto mb-3">
                    3
                  </div>
                  <h4 className="font-semibold mb-2">Complications</h4>
                  <p className="text-sm text-muted-foreground">
                    Bleeding, seizures, and neurological symptoms can develop over time
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Clinical Significance */}
        <div className="mt-12">
          <Card className="bg-info-light border-info/30">
            <CardHeader>
              <CardTitle className="flex items-centre gap-2 text-info">
                <Info className="h-5 w-5" />
                Clinical Significance
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-info text-sm">
                Understanding AVM anatomy is crucial for treatment planning. The abnormal vascular structure 
                explains why AVMs bleed and cause symptoms, while the relationship to normal brain tissue 
                determines treatment approach and risk. The high-flow nature of AVMs makes them visible on 
                imaging and guides the choice between surgical, endovascular, and radiation treatments.
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
}

export default AVMAnatomySection;
