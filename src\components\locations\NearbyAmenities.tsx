import React from 'react';

interface AmenityItem {
  name: string;
  description: string;
}

interface AmenityCategory {
  title: string;
  items: AmenityItem[];
}

interface NearbyAmenitiesProps {
  title: string;
  subtitle: string;
  description: string;
  categories: AmenityCategory[];
}

/**
 * Nearby Amenities Component
 * Displays categorized nearby amenities (cafes, shopping, parks, etc.)
 * Preserves all original nearby amenities content from Surrey Hills location page
 */
const NearbyAmenities: React.FC<NearbyAmenitiesProps> = ({
  title,
  subtitle,
  description,
  categories
}) => {
  return (
    <section className="py-16 bg-primary/5">
      <div className="container">
        <div className="mb-12">
          <h2 className="text-enhanced-heading text-3xl font-bold mb-4">{title}</h2>
          <p className="text-muted-foreground">
            {subtitle}
          </p>
        </div>

        <div className="mt-8 max-w-3xl mx-auto mb-12">
          <p className="text-enhanced-body text-center">
            {description}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {categories.map((category, categoryIndex) => (
            <div key={categoryIndex} className="card p-6 rounded-lg shadow-md medical-card">
              <h3 className="text-xl font-semibold mb-3 text-primary">{category.title}</h3>
              <ul className="text-muted-foreground list-none space-y-3">
                {category.items.map((item, itemIndex) => (
                  <li key={itemIndex} className="flex items-start">
                    <span className="text-primary mr-2">•</span>
                    <div>
                      <span className="font-medium">{item.name}</span> - {item.description}
                    </div>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

NearbyAmenities.displayName = 'NearbyAmenities';

export default NearbyAmenities;
