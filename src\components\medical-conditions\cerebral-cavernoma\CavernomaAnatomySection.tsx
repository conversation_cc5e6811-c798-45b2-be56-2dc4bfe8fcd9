import { Brain, Layers, Droplets, Info, ChevronDown, ChevronUp, Activity, Hexagon } from 'lucide-react';
import React, { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Collapsible, CollapsibleContent } from '@/components/ui/collapsible';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface VascularComponent {
  component: string;
  description: string;
  function: string[];
  cavernomaCharacteristics: string[];
}

interface CavernomaAnatomySectionProps {
  title: string;
  description: string;
  vascularStructure: VascularComponent[];
}

export function CavernomaAnatomySection({ 
  title, 
  description, 
  vascularStructure 
}: CavernomaAnatomySectionProps) {
  const deviceInfo = useDeviceDetection();
  const [expandedComponent, setExpandedComponent] = useState<string | null>(null);

  const toggleExpanded = (component: string) => {
    setExpandedComponent(expandedComponent === component ? null : component);
  };

  const getComponentIcon = (component: string) => {
    if (component.includes('Normal')) return Brain;
    if (component.includes('Cavernoma')) return Hexagon;
    if (component.includes('Surrounding')) return Layers;
    return Activity;
  };

  const getComponentColor = (component: string) => {
    if (component.includes('Normal')) return 'text-foreground bg-info-light border-info/30';
    if (component.includes('Cavernoma')) return 'text-foreground bg-muted-light border-border/70';
    if (component.includes('Surrounding')) return 'text-foreground bg-success-light border-success/30';
    return 'text-muted-foreground bg-muted border-border';
  };

  const getAbnormalityLevel = (component: string) => {
    if (component.includes('Normal')) {
      return { level: 'Normal Structure', colour: 'badge-routine' };
    }
    if (component.includes('Cavernoma')) {
      return { level: 'Abnormal Structure', colour: 'badge-emergency' };
    }
    return { level: 'Affected Tissue', colour: 'badge-info' };
  };

  return (
    <section className={cn("py-16", deviceInfo.isMobile ? "px-4" : "")}>
      <div className="container">
        <div className="text-centre mb-12">
          <h2 className={cn(
            "font-bold mb-4",
            deviceInfo.isMobile ? "text-2xl" : "text-3xl"
          )}>
            {title}
          </h2>
          <p className={cn(
            "text-muted-foreground max-w-3xl mx-auto",
            deviceInfo.isMobile ? "text-sm" : "text-lg"
          )}>
            {description}
          </p>
        </div>

        {/* Anatomical Overview */}
        <div className="mb-12">
          <Card className="bg-muted">
            <CardContent className="pt-6">
              <div className={cn(
                "grid gap-8 items-centre",
                deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2"
              )}>
                <div>
                  <h3 className="text-xl font-semibold mb-4">Cavernoma vs Normal Blood Vessels</h3>
                  <p className="text-muted-foreground mb-4">
                    Cavernomas are abnormal clusters of blood vessels that lack the normal structural components 
                    of healthy brain blood vessels. Understanding this difference helps explain why cavernomas 
                    are prone to bleeding and can cause neurological symptoms.
                  </p>
                  <div className="space-y-2">
                    <div className="flex items-centre gap-2">
                      <div className="w-3 h-3 bg-info rounded-full"></div>
                      <span className="text-sm">Normal Blood Vessels</span>
                    </div>
                    <div className="flex items-centre gap-2">
                      <div className="w-3 h-3 bg-muted rounded-full"></div>
                      <span className="text-sm">Cavernoma Structure</span>
                    </div>
                    <div className="flex items-centre gap-2">
                      <div className="w-3 h-3 bg-success rounded-full"></div>
                      <span className="text-sm">Surrounding Brain Tissue</span>
                    </div>
                  </div>
                </div>
                <div className="flex justify-centre">
                  <div className="relative">
                    <img 
                      src="/images/brain-conditions/brain-anatomy-detailed.jpg" 
                      alt="Cavernoma anatomy diagram showing vascular malformation"
                      className="rounded-lg shadow-lg max-w-full h-auto"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-lg"></div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Vascular Component Details */}
        <div className="space-y-6">
          {vascularStructure.map((component, index) => {
            const Icon = getComponentIcon(component.component);
            const isExpanded = expandedComponent === component.component;
            const abnormalityLevel = getAbnormalityLevel(component.component);
            
            return (
              <Card key={index} className={cn("transition-all duration-200", getComponentColor(component.component))}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="p-2 rounded-full bg-background/80">
                        <Icon className="h-5 w-5" />
                      </div>
                      <div>
                        <CardTitle className="text-lg">{component.component}</CardTitle>
                        <CardDescription className="text-sm">{component.description}</CardDescription>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge className={abnormalityLevel.colour}>
                        {abnormalityLevel.level}
                      </Badge>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => toggleExpanded(component.component)}
                        className="flex items-center gap-1"
                      >
                        <Info className="h-4 w-4" />
                        {isExpanded ? (
                          <ChevronUp className="h-4 w-4" />
                        ) : (
                          <ChevronDown className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                
                <Collapsible open={isExpanded}>
                  <CollapsibleContent>
                    <CardContent className="pt-0">
                      <div className={cn(
                        "grid gap-6",
                        deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2"
                      )}>
                        {/* Functions */}
                        <div>
                          <h4 className="font-semibold mb-3">Key Functions</h4>
                          <ul className="space-y-2">
                            {component.function.map((func, idx) => (
                              <li key={idx} className="flex items-start gap-2">
                                <div className="w-2 h-2 bg-current rounded-full mt-2 flex-shrink-0 opacity-60" />
                                <span className="text-sm">{func}</span>
                              </li>
                            ))}
                          </ul>
                        </div>

                        {/* Cavernoma Characteristics */}
                        <div>
                          <h4 className="font-semibold mb-3">Cavernoma-Related Features</h4>
                          <ul className="space-y-2">
                            {component.cavernomaCharacteristics.map((characteristic, idx) => (
                              <li key={idx} className="flex items-start gap-2">
                                <div className="w-2 h-2 bg-current rounded-full mt-2 flex-shrink-0 opacity-60" />
                                <span className="text-sm">{characteristic}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>

                      {/* Additional Information */}
                      <div className="mt-6 p-4 bg-background/50 rounded-lg">
                        <h4 className="font-semibold mb-2">Clinical Significance</h4>
                        <p className="text-sm text-muted-foreground">
                          {component.component.includes('Normal') && 
                            "Normal brain blood vessels have a complex structure with multiple layers that provide strength and regulate blood flow. The absence of these features in cavernomas explains their tendency to bleed."
                          }
                          {component.component.includes('Cavernoma') && 
                            "Cavernomas lack the normal vessel wall components, making them fragile and prone to bleeding. The characteristic 'mulberry' or 'popcorn' appearance on MRI reflects blood products from repeated small bleeds."
                          }
                          {component.component.includes('Surrounding') && 
                            "The brain tissue around cavernomas often shows evidence of previous bleeding with hemosiderin deposits. This tissue may be the source of seizures and can be affected by surgical treatment."
                          }
                        </p>
                      </div>
                    </CardContent>
                  </CollapsibleContent>
                </Collapsible>
              </Card>
            );
          })}
        </div>

        {/* Cavernoma Formation Process */}
        <div className="mt-12">
          <Card className="bg-muted">
            <CardHeader>
              <CardTitle className="flex items-centre gap-2">
                <Droplets className="h-5 w-5 text-foreground" />
                How Cavernomas Develop and Cause Problems
              </CardTitle>
              <CardDescription>
                Understanding the development and complications of cavernomas
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className={cn(
                "grid gap-6",
                deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-3"
              )}>
                <div className="text-centre">
                  <div className="w-12 h-12 bg-muted text-primary-foreground rounded-full flex items-centre justify-centre font-bold mx-auto mb-3">
                    1
                  </div>
                  <h4 className="font-semibold mb-2">Abnormal Development</h4>
                  <p className="text-sm text-muted-foreground">
                    Blood vessels develop abnormally, lacking normal structural components
                  </p>
                </div>
                <div className="text-centre">
                  <div className="w-12 h-12 bg-muted text-primary-foreground rounded-full flex items-centre justify-centre font-bold mx-auto mb-3">
                    2
                  </div>
                  <h4 className="font-semibold mb-2">Microbleeding</h4>
                  <p className="text-sm text-muted-foreground">
                    Fragile vessel walls lead to repeated small bleeds over time
                  </p>
                </div>
                <div className="text-centre">
                  <div className="w-12 h-12 bg-muted text-primary-foreground rounded-full flex items-centre justify-centre font-bold mx-auto mb-3">
                    3
                  </div>
                  <h4 className="font-semibold mb-2">Symptom Development</h4>
                  <p className="text-sm text-muted-foreground">
                    Bleeding and mass effect can cause seizures and neurological symptoms
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Clinical Significance */}
        <div className="mt-12">
          <Card className="bg-info-light border-info/30">
            <CardHeader>
              <CardTitle className="flex items-centre gap-2 text-info">
                <Info className="h-5 w-5" />
                Clinical Significance
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-info text-sm">
                Understanding cavernoma anatomy is crucial for treatment planning. The abnormal vessel structure 
                explains why cavernomas bleed and cause symptoms, while the relationship to surrounding brain tissue 
                determines surgical approach and risk. The characteristic MRI appearance helps distinguish cavernomas 
                from other brain lesions and guides monitoring strategies.
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
}

export default CavernomaAnatomySection;
