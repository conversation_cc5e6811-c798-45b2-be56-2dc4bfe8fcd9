import { Brain, Network, Layers, Info, ChevronDown, ChevronUp, Activity, Zap, Droplets } from 'lucide-react';
import React, { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Collapsible, CollapsibleContent } from '@/components/ui/collapsible';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface BrainStructure {
  structure: string;
  description: string;
  normalFunction: string[];
  malformationEffects: string[];
}

interface BrainAnatomySectionProps {
  title: string;
  description: string;
  brainStructures: BrainStructure[];
}

export function BrainAnatomySection({ 
  title, 
  description, 
  brainStructures 
}: BrainAnatomySectionProps) {
  const deviceInfo = useDeviceDetection();
  const [expandedStructure, setExpandedStructure] = useState<string | null>(null);

  const toggleExpanded = (structure: string) => {
    setExpandedStructure(expandedStructure === structure ? null : structure);
  };

  const getStructureIcon = (structure: string) => {
    if (structure.includes('Cerebellum')) return Brain;
    if (structure.includes('Foramen Magnum')) return Layers;
    if (structure.includes('Brainstem')) return Network;
    return Activity;
  };

  const getStructureColor = (structure: string) => {
    if (structure.includes('Cerebellum')) return 'text-primary bg-primary/10 border-primary/50';
    if (structure.includes('Foramen Magnum')) return 'text-foreground dark:text-foreground bg-success/10 border-success/50';
    if (structure.includes('Brainstem')) return 'text-medical-blue dark:text-medical-blue bg-medical-blue/10 border-medical-blue/50';
    return 'text-muted-foreground bg-muted border-border';
  };

  const getStructureImportance = (structure: string) => {
    if (structure.includes('Cerebellum')) {
      return { level: 'Primary Affected', colour: 'bg-primary/10 text-primary' };
    }
    if (structure.includes('Foramen Magnum')) {
      return { level: 'Anatomical Boundary', colour: 'bg-success/10 text-success dark:text-success' };
    }
    return { level: 'Secondary Affected', colour: 'bg-medical-blue/10 text-medical-blue dark:text-medical-blue' };
  };

  return (
    <section className={cn("py-16", deviceInfo.isMobile ? "px-4" : "")}>
      <div className="container">
        <div className="text-centre mb-12">
          <h2 className={cn(
            "font-bold mb-4",
            deviceInfo.isMobile ? "text-2xl" : "text-3xl"
          )}>
            {title}
          </h2>
          <p className={cn(
            "text-muted-foreground max-w-3xl mx-auto",
            deviceInfo.isMobile ? "text-sm" : "text-lg"
          )}>
            {description}
          </p>
        </div>

        {/* Anatomical Overview */}
        <div className="mb-12">
          <Card className="bg-muted">
            <CardContent className="pt-6">
              <div className={cn(
                "grid gap-8 items-centre",
                deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2"
              )}>
                <div>
                  <h3 className="text-xl font-semibold mb-4">Chiari Malformation and Brain Anatomy</h3>
                  <p className="text-muted-foreground mb-4">
                    Chiari malformation occurs when brain tissue extends into the spinal canal due to a skull 
                    that is abnormally small or misshapen. This displacement affects the normal flow of 
                    cerebrospinal fluid and can compress vital brain structures.
                  </p>
                  <div className="space-y-2">
                    <div className="flex items-centre gap-2">
                      <div className="w-3 h-3 bg-info rounded-full"></div>
                      <span className="text-sm">Cerebellum (Balance & Coordination)</span>
                    </div>
                    <div className="flex items-centre gap-2">
                      <div className="w-3 h-3 bg-success rounded-full"></div>
                      <span className="text-sm">Foramen Magnum (Skull Opening)</span>
                    </div>
                    <div className="flex items-centre gap-2">
                      <div className="w-3 h-3 bg-medical-blue rounded-full"></div>
                      <span className="text-sm">Brainstem (Vital Functions)</span>
                    </div>
                  </div>
                </div>
                <div className="flex justify-centre">
                  <div className="relative">
                    <img 
                      src="/images/brain-conditions/brain-anatomy-detailed.jpg" 
                      alt="Chiari malformation anatomy showing cerebellar tonsillar herniation"
                      className="rounded-lg shadow-lg max-w-full h-auto"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-lg"></div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Brain Structure Details */}
        <div className="space-y-6">
          {brainStructures.map((structure, index) => {
            const Icon = getStructureIcon(structure.structure);
            const isExpanded = expandedStructure === structure.structure;
            const structureImportance = getStructureImportance(structure.structure);
            
            return (
              <Card key={index} className={cn("transition-all duration-200", getStructureColor(structure.structure))}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="p-2 rounded-full bg-background/80">
                        <Icon className="h-5 w-5" />
                      </div>
                      <div>
                        <CardTitle className="text-lg">{structure.structure}</CardTitle>
                        <CardDescription className="text-sm">{structure.description}</CardDescription>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge className={structureImportance.colour}>
                        {structureImportance.level}
                      </Badge>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => toggleExpanded(structure.structure)}
                        className="flex items-center gap-1"
                      >
                        <Info className="h-4 w-4" />
                        {isExpanded ? (
                          <ChevronUp className="h-4 w-4" />
                        ) : (
                          <ChevronDown className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                
                <Collapsible open={isExpanded}>
                  <CollapsibleContent>
                    <CardContent className="pt-0">
                      <div className={cn(
                        "grid gap-6",
                        deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-2"
                      )}>
                        {/* Normal Functions */}
                        <div>
                          <h4 className="font-semibold mb-3">Normal Functions</h4>
                          <ul className="space-y-2">
                            {structure.normalFunction.map((func, idx) => (
                              <li key={idx} className="flex items-start gap-2">
                                <div className="w-2 h-2 bg-current rounded-full mt-2 flex-shrink-0 opacity-60" />
                                <span className="text-sm">{func}</span>
                              </li>
                            ))}
                          </ul>
                        </div>

                        {/* Malformation Effects */}
                        <div>
                          <h4 className="font-semibold mb-3">Effects in Chiari Malformation</h4>
                          <ul className="space-y-2">
                            {structure.malformationEffects.map((effect, idx) => (
                              <li key={idx} className="flex items-start gap-2">
                                <div className="w-2 h-2 bg-current rounded-full mt-2 flex-shrink-0 opacity-60" />
                                <span className="text-sm">{effect}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>

                      {/* Additional Information */}
                      <div className="mt-6 p-4 bg-background/50 rounded-lg">
                        <h4 className="font-semibold mb-2">Clinical Significance</h4>
                        <p className="text-sm text-muted-foreground">
                          {structure.structure.includes('Cerebellum') && 
                            "The cerebellum is the primary structure affected in Chiari malformation. When cerebellar tonsils herniate through the foramen magnum, they can compress the brainstem and obstruct cerebrospinal fluid flow, leading to the characteristic symptoms of headaches, balance problems, and coordination difficulties."
                          }
                          {structure.structure.includes('Foramen Magnum') && 
                            "The foramen magnum is the critical anatomical boundary in Chiari malformation. When this opening becomes crowded with herniated brain tissue, it restricts normal cerebrospinal fluid flow and can compress vital neural structures, leading to increased intracranial pressure and neurological symptoms."
                          }
                          {structure.structure.includes('Brainstem') && 
                            "The brainstem controls vital functions including breathing, heart rate, and consciousness. In Chiari malformation, compression of the brainstem by herniated cerebellar tissue can lead to serious symptoms including sleep apnoea, swallowing difficulties, and in severe cases, life-threatening complications."
                          }
                        </p>
                      </div>
                    </CardContent>
                  </CollapsibleContent>
                </Collapsible>
              </Card>
            );
          })}
        </div>

        {/* Herniation Mechanism */}
        <div className="mt-12">
          <Card className="bg-info-light border border-info/30">
            <CardHeader>
              <CardTitle className="flex items-centre gap-2">
                <Zap className="h-5 w-5 text-info" />
                How Chiari Malformation Develops
              </CardTitle>
              <CardDescription>
                Understanding the mechanism of cerebellar tonsillar herniation in Chiari malformation
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className={cn(
                "grid gap-6",
                deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-3"
              )}>
                <div className="text-centre">
                  <div className="w-12 h-12 bg-primary text-primary-foreground rounded-full flex items-centre justify-centre font-bold mx-auto mb-3">
                    1
                  </div>
                  <h4 className="font-semibold mb-2">Skull Development</h4>
                  <p className="text-sm text-muted-foreground">
                    Abnormal skull development creates a smaller than normal posterior fossa space
                  </p>
                </div>
                <div className="text-centre">
                  <div className="w-12 h-12 bg-primary text-primary-foreground rounded-full flex items-centre justify-centre font-bold mx-auto mb-3">
                    2
                  </div>
                  <h4 className="font-semibold mb-2">Cerebellar Herniation</h4>
                  <p className="text-sm text-muted-foreground">
                    Cerebellar tonsils are pushed downward through the foramen magnum into the spinal canal
                  </p>
                </div>
                <div className="text-centre">
                  <div className="w-12 h-12 bg-primary text-primary-foreground rounded-full flex items-centre justify-centre font-bold mx-auto mb-3">
                    3
                  </div>
                  <h4 className="font-semibold mb-2">Symptom Development</h4>
                  <p className="text-sm text-muted-foreground">
                    Compression and altered cerebrospinal fluid flow lead to characteristic symptoms
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* CSF Flow Information */}
        <div className="mt-12">
          <Card className="bg-info-light/30 border-info/50">
            <CardHeader>
              <CardTitle className="flex items-centre gap-2 text-info">
                <Droplets className="h-5 w-5" />
                Cerebrospinal Fluid Flow
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-info text-sm">
                Normal cerebrospinal fluid flow is essential for brain health. In Chiari malformation, 
                herniated cerebellar tissue can obstruct this flow at the foramen magnum, leading to 
                increased pressure and the development of associated conditions like syringomyelia. 
                Surgical decompression aims to restore normal flow patterns and relieve symptoms.
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
}

export default BrainAnatomySection;
