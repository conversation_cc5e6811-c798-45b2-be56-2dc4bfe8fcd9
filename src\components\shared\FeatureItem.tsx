import React from 'react';
import { Link } from 'react-router-dom';

import IconContainer from './IconContainer';

import { cn } from '@/lib/utils';

interface FeatureItemProps {
  title: string;
  description: string;
  icon?: React.ReactNode;
  link?: string;
  layout?: 'vertical' | 'horizontal';
  alignment?: 'left' | 'center' | 'right';
  iconVariant?: 'primary' | 'secondary' | 'muted' | 'accent';
  iconSize?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  titleClassName?: string;
  descriptionClassName?: string;
}

const FeatureItem: React.FC<FeatureItemProps> = ({
  title,
  description,
  icon,
  link,
  layout = 'vertical',
  alignment = 'center',
  iconVariant = 'primary',
  iconSize = 'md',
  className = '',
  titleClassName = '',
  descriptionClassName = ''
}) => {
  const getAlignmentClass = () => {
    switch (alignment) {
      case 'left':
        return 'text-left items-start';
      case 'right':
        return 'text-right items-end';
      default:
        return 'text-center items-center';
    }
  };

  const getLayoutClass = () => {
    switch (layout) {
      case 'horizontal':
        return 'flex-row space-x-4';
      default:
        return 'flex-col space-y-4';
    }
  };

  const content = (
    <div className={cn(
      'flex',
      getLayoutClass(),
      getAlignmentClass(),
      'p-6 rounded-lg transition-colors',
      link && 'hover:bg-muted/50 cursor-pointer',
      className
    )}>
      {icon && (
        <IconContainer
          icon={icon}
          variant={iconVariant}
          size={iconSize}
        />
      )}
      
      <div className={cn(
        layout === 'horizontal' ? 'flex-1' : '',
        alignment === 'centre' && layout === 'vertical' ? 'text-centre' : ''
      )}>
        <h3 className={cn(
          'text-xl font-semibold mb-3',
          titleClassName
        )}>
          {title}
        </h3>
        <p className={cn(
          'text-muted-foreground',
          descriptionClassName
        )}>
          {description}
        </p>
      </div>
    </div>
  );

  if (link) {
    return (
      <Link to={link} className="block">
        {content}
      </Link>
    );
  }

  return content;
};

export default FeatureItem;
