import React from 'react';
import { Link } from 'react-router-dom';

import { Button } from '@/components/ui/button';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface MainTreatmentAreasSectionProps {
  procedureDescriptions: {
    [key: string]: { name: string };
  };
  expertiseCards: {
    brainConditions: { description: string };
    spinalProblems: { description: string };
    nerveProblems: { description: string };
    medicolegalReports: { title: string; description: string };
  };
  learnMoreText: string;
}

const MainTreatmentAreasSection: React.FC<MainTreatmentAreasSectionProps> = ({
  procedureDescriptions,
  expertiseCards,
  learnMoreText
}) => {
  const deviceInfo = useDeviceDetection();

  return (
    <section className={deviceInfo.isMobile ? "mobile-section" : "py-16"}>
      <div className={deviceInfo.isMobile ? "mobile-container" : "container"}>
        <div className={cn(
          deviceInfo.isMobile
            ? "grid grid-cols-1 gap-mobile-lg"
            : "grid grid-cols-1 md:grid-cols-2 gap-12"
        )}>
          {/* Brain Conditions */}
          <div className={cn(
            "card rounded-lg shadow-md bg-card",
            deviceInfo.isMobile ? "p-mobile-lg" : "p-8"
          )}>
            <div className={cn(
              "flex justify-center",
              deviceInfo.isMobile ? "mb-mobile-md" : "mb-4"
            )}>
              <img
                src="/images/brain-abstract-icon.png"
                alt="Brain Conditions"
                className={cn(
                  "object-contain",
                  deviceInfo.isMobile ? "h-16 w-16" : "h-20 w-20"
                )}
              />
            </div>
            <h3 className={cn(
              "font-semibold text-primary text-centre mb-mobile-sm",
              deviceInfo.isMobile
                ? "mobile-subheading"
                : "text-xl mb-3"
            )}>
              {procedureDescriptions["brain-tumour-removal"].name}
            </h3>
            <p className={cn(
              "text-muted-foreground mb-mobile-lg",
              deviceInfo.isMobile ? "mobile-text" : "mb-6"
            )}>
              {expertiseCards.brainConditions.description}
            </p>
          </div>

          {/* Spinal Problems */}
          <div className="card p-8 rounded-lg shadow-md medical-card">
            <div className="flex justify-centre mb-4">
              <img
                src="/images/vertebra-icon.png"
                alt="Spinal Problems"
                className="h-20 w-20 object-contain"
              />
            </div>
            <h3 className="text-xl font-semibold mb-3 text-primary text-centre">
              {procedureDescriptions["lumbar-disc-replacement"].name}
            </h3>
            <p className="text-muted-foreground mb-6">
              {expertiseCards.spinalProblems.description}
            </p>
          </div>

          {/* Nerve Problems */}
          <div className="card p-8 rounded-lg shadow-md medical-card">
            <div className="flex justify-centre mb-4">
              <img
                src="/images/neuron.png"
                alt="Nerve Problems"
                className="h-20 w-20 object-contain"
              />
            </div>
            <h3 className="text-xl font-semibold mb-3 text-primary text-centre">
              {procedureDescriptions["peripheral-nerve-surgery"].name}
            </h3>
            <p className="text-muted-foreground mb-6">
              {expertiseCards.nerveProblems.description}
            </p>
          </div>

          {/* Medico-Legal Reports */}
          <div className="card p-8 rounded-lg shadow-md medical-card">
            <div className="flex justify-center mb-4">
              <img
                src="/images/libra-icon.png"
                alt="Medico-Legal Reports"
                className="h-20 w-20 object-contain"
              />
            </div>
            <h3 className="text-xl font-semibold mb-3 text-primary text-center">
              {expertiseCards.medicolegalReports.title}
            </h3>
            <p className="text-muted-foreground mb-6">
              {expertiseCards.medicolegalReports.description}
            </p>
            <div className="text-center">
              <Button asChild variant="outline">
                <Link to="/medicolegal">{learnMoreText}</Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

MainTreatmentAreasSection.displayName = 'MainTreatmentAreasSection';

export default MainTreatmentAreasSection;
