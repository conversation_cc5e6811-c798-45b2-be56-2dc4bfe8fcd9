import { 
  Shield, 
  Home, 
  Briefcase, 
  Shirt, 
  Weight, 
  CheckCircle,
  AlertTriangle,
  Target,
  Activity,
  Heart,
  TrendingUp,
  Settings
} from "lucide-react";
import React, { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface ErgonomicRecommendation {
  id: string;
  title: string;
  description: string;
  importance: 'High' | 'Medium' | 'Low';
  recommendations: string[];
  avoidActions: string[];
  scientificRationale: string;
  implementationTips: string[];
}

interface LifestyleCategory {
  id: string;
  title: string;
  icon: React.ComponentType<any>;
  description: string;
  recommendations: ErgonomicRecommendation[];
}

interface MeralgiaErgonomicsSectionProps {
  className?: string;
}

const lifestyleCategories: LifestyleCategory[] = [
  {
    id: 'workplace',
    title: 'Workplace Ergonomics',
    icon: Briefcase,
    description: 'Optimise your work environment to reduce nerve compression and prevent symptom recurrence',
    recommendations: [
      {
        id: 'standing-work',
        title: 'Standing Work Positions',
        description: 'Proper positioning and support for jobs requiring prolonged standing',
        importance: 'High',
        recommendations: [
          'Use anti-fatigue mats to reduce pressure on feet and legs',
          'Alternate weight between feet every 15-20 minutes',
          'Take sitting breaks every 30-45 minutes',
          'Wear supportive, well-cushioned shoes',
          'Maintain proper posture with shoulders back and core engaged'
        ],
        avoidActions: [
          'Standing in one position for hours without breaks',
          'Wearing high heels or unsupportive footwear',
          'Leaning on one leg consistently',
          'Working on hard concrete floors without mats'
        ],
        scientificRationale: 'Prolonged standing increases pressure on the inguinal ligament and can compress the lateral femoral cutaneous nerve. Regular position changes and proper support reduce this compression.',
        implementationTips: [
          'Set hourly reminders to change positions',
          'Invest in quality anti-fatigue mats',
          'Consider adjustable height workstations',
          'Use footrests or rails when available'
        ]
      },
      {
        id: 'tool-belts',
        title: 'Tool Belt and Equipment Management',
        description: 'Proper use and positioning of work tools and equipment to minimise nerve compression',
        importance: 'High',
        recommendations: [
          'Use suspender-style tool belts instead of waist belts',
          'Distribute weight evenly across both shoulders',
          'Choose lightweight, ergonomic tools when possible',
          'Position frequently used tools within easy reach',
          'Take regular breaks to remove heavy equipment'
        ],
        avoidActions: [
          'Wearing tight tool belts around the waist',
          'Carrying excessive weight on one side',
          'Using belts that dig into the hip area',
          'Ignoring discomfort from equipment pressure'
        ],
        scientificRationale: 'Tool belts and equipment can create direct pressure on the lateral femoral cutaneous nerve at the inguinal ligament, leading to compression and symptoms.',
        implementationTips: [
          'Try different belt styles to find the most comfortable',
          'Adjust belt tension throughout the day',
          'Consider tool vests as an alternative',
          'Regularly assess and minimise carried weight'
        ]
      }
    ]
  },
  {
    id: 'clothing',
    title: 'Clothing and Accessories',
    icon: Shirt,
    description: 'Choose appropriate clothing and accessories that don\'t contribute to nerve compression',
    recommendations: [
      {
        id: 'belt-selection',
        title: 'Belt and Waistband Choices',
        description: 'Selecting appropriate belts and clothing to avoid nerve compression',
        importance: 'High',
        recommendations: [
          'Choose wider, softer belts that distribute pressure',
          'Wear belts loosely, allowing for finger space',
          'Select pants with elastic or adjustable waistbands',
          'Consider suspenders as an alternative to belts',
          'Opt for low-rise or mid-rise pants over high-waisted styles'
        ],
        avoidActions: [
          'Wearing tight, narrow belts',
          'Cinching belts too tightly',
          'Using belts with large, hard buckles',
          'Wearing restrictive, high-waisted clothing'
        ],
        scientificRationale: 'Tight belts and waistbands can directly compress the lateral femoral cutaneous nerve as it passes under the inguinal ligament, triggering or worsening symptoms.',
        implementationTips: [
          'Buy belts one size larger than usual',
          'Check belt tightness throughout the day',
          'Consider fabric belts over leather',
          'Adjust clothing after meals when abdomen expands'
        ]
      },
      {
        id: 'fabric-choices',
        title: 'Fabric and Fit Considerations',
        description: 'Choosing appropriate fabrics and fits to minimise skin sensitivity and pressure',
        importance: 'Medium',
        recommendations: [
          'Select soft, breathable fabrics like cotton or bamboo',
          'Choose loose-fitting clothing around the hip and thigh area',
          'Avoid seams or decorations over the lateral thigh',
          'Consider seamless or flat-seam construction',
          'Opt for moisture-wicking materials for active wear'
        ],
        avoidActions: [
          'Wearing rough or scratchy fabrics',
          'Choosing tight-fitting pants or leggings',
          'Ignoring clothing that causes irritation',
          'Wearing clothes with prominent seams over affected area'
        ],
        scientificRationale: 'Hypersensitive skin in meralgia paresthetica can be irritated by rough fabrics or tight clothing, potentially worsening symptoms and discomfort.',
        implementationTips: [
          'Test new fabrics on unaffected skin first',
          'Wash new clothes before wearing',
          'Consider tagless clothing options',
          'Keep a wardrobe of comfortable, tested clothing'
        ]
      }
    ]
  },
  {
    id: 'daily-activities',
    title: 'Daily Activities',
    icon: Home,
    description: 'Modify daily activities and habits to support nerve health and prevent symptom aggravation',
    recommendations: [
      {
        id: 'sitting-posture',
        title: 'Sitting and Posture Management',
        description: 'Proper sitting techniques and posture to reduce nerve compression',
        importance: 'High',
        recommendations: [
          'Sit with feet flat on floor and knees at 90 degrees',
          'Use lumbar support to maintain natural spine curve',
          'Take standing breaks every 30-45 minutes',
          'Avoid crossing legs for extended periods',
          'Position hips slightly higher than knees when sitting'
        ],
        avoidActions: [
          'Slouching or leaning to one side',
          'Sitting for hours without breaks',
          'Using chairs without proper support',
          'Sitting with wallet in back pocket'
        ],
        scientificRationale: 'Proper sitting posture reduces hip flexion and decreases tension on the lateral femoral cutaneous nerve, while poor posture can increase compression.',
        implementationTips: [
          'Set reminders to check posture hourly',
          'Use ergonomic seat cushions if needed',
          'Adjust chair height and support regularly',
          'Practice good posture habits consistently'
        ]
      },
      {
        id: 'sleep-positioning',
        title: 'Sleep Position and Bed Setup',
        description: 'Optimizing sleep environment and positioning for nerve comfort',
        importance: 'Medium',
        recommendations: [
          'Sleep on your back or unaffected side',
          'Use a pillow between knees when side sleeping',
          'Ensure mattress provides adequate support',
          'Avoid sleeping on stomach if it worsens symptoms',
          'Consider a body pillow for additional support'
        ],
        avoidActions: [
          'Sleeping in positions that increase hip flexion',
          'Using too many pillows under head',
          'Sleeping on very soft or very firm surfaces',
          'Ignoring sleep position comfort'
        ],
        scientificRationale: 'Sleep positioning affects nerve tension and compression. Proper alignment during sleep allows for nerve recovery and reduces morning stiffness.',
        implementationTips: [
          'Experiment with different pillow arrangements',
          'Consider mattress toppers for comfort',
          'Use positioning aids if helpful',
          'Maintain consistent sleep schedule'
        ]
      }
    ]
  },
  {
    id: 'weight-management',
    title: 'Weight and Fitness',
    icon: Weight,
    description: 'Maintain healthy weight and fitness levels to reduce pressure on nerves and improve overall health',
    recommendations: [
      {
        id: 'weight-control',
        title: 'Healthy Weight Maintenance',
        description: 'Strategies for achieving and maintaining optimal weight to reduce nerve compression',
        importance: 'High',
        recommendations: [
          'Maintain BMI within healthy range (18.5-24.9)',
          'Focus on gradual, sustainable weight loss if needed',
          'Combine cardiovascular exercise with strength training',
          'Follow balanced, anti-inflammatory diet',
          'Monitor weight changes during pregnancy'
        ],
        avoidActions: [
          'Rapid weight loss or gain',
          'Extreme dieting or restrictions',
          'Ignoring weight-related symptom changes',
          'Sedentary lifestyle'
        ],
        scientificRationale: 'Excess weight increases abdominal pressure and can compress the lateral femoral cutaneous nerve. Healthy weight reduces this pressure and improves overall nerve health.',
        implementationTips: [
          'Set realistic weight loss goals (1-2 lbs per week)',
          'Track progress with measurements, not just weight',
          'Work with healthcare providers for guidance',
          'Focus on lifestyle changes, not quick fixes'
        ]
      }
    ]
  }
];

const MeralgiaErgonomicsSection: React.FC<MeralgiaErgonomicsSectionProps> = ({ className }) => {
  const deviceInfo = useDeviceDetection();
  const [selectedCategory, setSelectedCategory] = useState<string>('workplace');

  const getImportanceColor = (importance: string) => {
    switch (importance) {
      case 'High': return 'bg-muted-light text-foreground border border-border/30';
      case 'Medium': return 'bg-info-light text-info border border-info/30';
      case 'Low': return 'bg-success-light text-success border border-success/30';
      default: return 'bg-muted text-muted-foreground';
    }
  };

  return (
    <section className={cn(
      "section-background-alt border-y border-border/50",
      deviceInfo.isMobile ? "py-16" : "py-24",
      className
    )}>
      <div className="container">
        {/* Section Header */}
        <div className="text-center mb-20">
          <Badge variant="info" className="mb-6">
            <Shield className="w-4 h-4 mr-2" />
            Ergonomics & Lifestyle
          </Badge>
          <h2 className={cn(
            "font-bold text-foreground mb-8 leading-tight",
            deviceInfo.isMobile ? "text-2xl" : "text-3xl lg:text-4xl"
          )}>
            Ergonomic and Lifestyle Recommendations
          </h2>
          <p className={cn(
            "text-foreground/80 max-w-4xl mx-auto leading-relaxed font-medium",
            deviceInfo.isMobile ? "text-base" : "text-lg"
          )}>
            Evidence-based lifestyle modifications to prevent nerve compression and support long-term recovery
          </p>
        </div>

        {/* Category Tabs */}
        <Tabs value={selectedCategory} onValueChange={setSelectedCategory} className="w-full">
          <TabsList className={cn(
            "grid w-full mb-12",
            deviceInfo.isMobile ? "grid-cols-2 h-auto" : "grid-cols-4 h-14"
          )}>
            {lifestyleCategories.map((category) => {
              const IconComponent = category.icon;
              return (
                <TabsTrigger 
                  key={category.id} 
                  value={category.id}
                  className={cn(
                    "flex items-center gap-2 font-medium",
                    deviceInfo.isMobile ? "flex-col py-3 px-2 text-xs" : "text-sm"
                  )}
                >
                  <IconComponent className={cn(
                    deviceInfo.isMobile ? "w-4 h-4" : "w-5 h-5"
                  )} />
                  <span className={deviceInfo.isMobile ? "text-center" : ""}>
                    {category.title}
                  </span>
                </TabsTrigger>
              );
            })}
          </TabsList>

          {/* Category Content */}
          {lifestyleCategories.map((category) => (
            <TabsContent key={category.id} value={category.id} className="space-y-8">
              {/* Category Description */}
              <Card className="medical-card">
                <CardHeader>
                  <CardTitle className="text-enhanced-heading flex items-centre gap-3">
                    <category.icon className="w-5 h-5 text-primary" />
                    {category.title}
                  </CardTitle>
                  <p className="text-enhanced-body">{category.description}</p>
                </CardHeader>
              </Card>

              {/* Recommendations */}
              <div className="space-y-8">
                {category.recommendations.map((recommendation) => (
                  <Card key={recommendation.id} className="medical-card">
                    <CardHeader>
                      <div className="flex items-start justify-between">
                        <CardTitle className="text-enhanced-heading">{recommendation.title}</CardTitle>
                        <Badge className={getImportanceColor(recommendation.importance)}>
                          {recommendation.importance} Priority
                        </Badge>
                      </div>
                      <p className="text-enhanced-body">{recommendation.description}</p>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      {/* Recommendations and Avoid Actions */}
                      <div className={cn(
                        "grid gap-6",
                        deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-1 md:grid-cols-2"
                      )}>
                        <div>
                          <h4 className="text-enhanced-subheading font-semibold mb-3 flex items-centre gap-2">
                            <CheckCircle className="w-4 h-4 text-success" />
                            Recommended Actions
                          </h4>
                          <ul className="space-y-2">
                            {recommendation.recommendations.map((item, index) => (
                              <li key={index} className="flex items-start gap-2">
                                <CheckCircle className="w-4 h-4 text-success mt-0.5 flex-shrink-0" />
                                <span className="text-enhanced-body text-sm">{item}</span>
                              </li>
                            ))}
                          </ul>
                        </div>

                        <div>
                          <h4 className="text-enhanced-subheading font-semibold mb-3 flex items-centre gap-2">
                            <AlertTriangle className="w-4 h-4 text-foreground" />
                            Actions to Avoid
                          </h4>
                          <ul className="space-y-2">
                            {recommendation.avoidActions.map((item, index) => (
                              <li key={index} className="flex items-start gap-2">
                                <AlertTriangle className="w-4 h-4 text-foreground mt-0.5 flex-shrink-0" />
                                <span className="text-enhanced-body text-sm">{item}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>

                      {/* Scientific Rationale */}
                      <div className="bg-info/5 border border-info/20 rounded-lg p-4">
                        <h4 className="text-enhanced-subheading font-semibold mb-2 flex items-centre gap-2">
                          <Target className="w-4 h-4 text-info" />
                          Scientific Rationale
                        </h4>
                        <p className="text-enhanced-body text-sm">{recommendation.scientificRationale}</p>
                      </div>

                      {/* Implementation Tips */}
                      <div>
                        <h4 className="text-enhanced-subheading font-semibold mb-3 flex items-centre gap-2">
                          <Settings className="w-4 h-4 text-primary" />
                          Implementation Tips
                        </h4>
                        <ul className="space-y-2">
                          {recommendation.implementationTips.map((tip, index) => (
                            <li key={index} className="flex items-start gap-2">
                              <div className="w-1.5 h-1.5 rounded-full bg-primary mt-2 flex-shrink-0" />
                              <span className="text-enhanced-body text-sm">{tip}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>
          ))}
        </Tabs>

        {/* Quick Reference Guide */}
        <Card className="medical-card mt-12">
          <CardHeader>
            <CardTitle className="text-enhanced-heading flex items-centre gap-3">
              <TrendingUp className="w-5 h-5 text-primary" />
              Quick Reference Guide
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className={cn(
              "grid gap-6",
              deviceInfo.isMobile ? "grid-cols-1" : "grid-cols-1 md:grid-cols-3"
            )}>
              <div className="text-centre">
                <div className="p-4 rounded-xl bg-success/10 border border-success/20 mb-4">
                  <Shield className="w-8 h-8 text-success mx-auto" />
                </div>
                <h4 className="text-enhanced-subheading font-semibold mb-2">Prevention First</h4>
                <p className="text-enhanced-body text-sm">Focus on preventing nerve compression through proper ergonomics and lifestyle choices</p>
              </div>
              <div className="text-centre">
                <div className="p-4 rounded-xl bg-info/10 border border-info/20 mb-4">
                  <Activity className="w-8 h-8 text-info mx-auto" />
                </div>
                <h4 className="text-enhanced-subheading font-semibold mb-2">Gradual Changes</h4>
                <p className="text-enhanced-body text-sm">Implement modifications gradually to allow your body to adapt to new habits</p>
              </div>
              <div className="text-centre">
                <div className="p-4 rounded-xl bg-muted/50 border border-border/50 mb-4">
                  <Heart className="w-8 h-8 text-foreground mx-auto" />
                </div>
                <h4 className="text-enhanced-subheading font-semibold mb-2">Listen to Your Body</h4>
                <p className="text-enhanced-body text-sm">Pay attention to what triggers symptoms and adjust accordingly</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </section>
  );
};

export default MeralgiaErgonomicsSection;
