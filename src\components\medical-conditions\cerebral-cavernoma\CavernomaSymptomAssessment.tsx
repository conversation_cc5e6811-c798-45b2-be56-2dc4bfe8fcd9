import { <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, T<PERSON>dingUp, ArrowRight, ArrowLeft, Activity, Zap } from 'lucide-react';
import React, { useState, useId } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { useDeviceDetection } from '@/contexts/DeviceContext';
import { cn } from '@/lib/utils';

interface AssessmentQuestion {
  id: string;
  question: string;
  description?: string;
  options: Array<{
    value: string;
    label: string;
    score: number;
  }>;
}

interface AssessmentResult {
  totalScore: number;
  likelihood: 'low' | 'moderate' | 'high';
  recommendation: string;
  urgency: 'routine' | 'urgent' | 'immediate';
  nextSteps: string[];
  symptoms: string[];
}

const assessmentQuestions: AssessmentQuestion[] = [
  {
    id: 'seizure-history',
    question: 'Have you experienced any seizures or seizure-like episodes?',
    description: 'Seizures are the most common presenting symptom of cavernomas',
    options: [
      { value: 'new-seizures', label: 'Yes, new seizures that I\'ve never had before', score: 4 },
      { value: 'worsening-seizures', label: 'Yes, existing seizures that are getting worse', score: 3 },
      { value: 'possible-episodes', label: 'Possible seizure-like episodes or blackouts', score: 2 },
      { value: 'no-seizures', label: 'No seizures or seizure-like episodes', score: 0 }
    ]
  },
  {
    id: 'bleeding-symptoms',
    question: 'Have you experienced sudden severe headaches or neurological symptoms?',
    description: 'Sudden symptoms may indicate bleeding from a cavernoma',
    options: [
      { value: 'sudden-severe', label: 'Sudden severe headache with neurological symptoms', score: 4 },
      { value: 'sudden-headache', label: 'Sudden severe headache without other symptoms', score: 3 },
      { value: 'progressive-symptoms', label: 'Gradually worsening headaches or symptoms', score: 2 },
      { value: 'no-sudden-symptoms', label: 'No sudden or severe symptoms', score: 0 }
    ]
  },
  {
    id: 'neurological-deficits',
    question: 'Have you noticed any weakness, numbness, or coordination problems?',
    description: 'Focal neurological symptoms may indicate cavernoma location and effects',
    options: [
      { value: 'progressive-deficits', label: 'Progressive weakness, numbness, or coordination problems', score: 3 },
      { value: 'intermittent-symptoms', label: 'Intermittent neurological symptoms', score: 2 },
      { value: 'mild-symptoms', label: 'Mild or subtle neurological changes', score: 1 },
      { value: 'no-deficits', label: 'No weakness, numbness, or coordination problems', score: 0 }
    ]
  },
  {
    id: 'family-history',
    question: 'Do you have a family history of cavernomas or multiple brain lesions?',
    description: 'Family history may indicate familial cavernomatosis',
    options: [
      { value: 'family-cavernomas', label: 'Yes, family members diagnosed with cavernomas', score: 3 },
      { value: 'family-brain-lesions', label: 'Yes, family history of brain lesions or seizures', score: 2 },
      { value: 'uncertain-history', label: 'Uncertain about family medical history', score: 1 },
      { value: 'no-family-history', label: 'No known family history of brain lesions', score: 0 }
    ]
  },
  {
    id: 'imaging-findings',
    question: 'Have you had brain imaging that showed abnormal findings?',
    description: 'Previous imaging may have detected cavernomas or suspicious lesions',
    options: [
      { value: 'confirmed-cavernoma', label: 'Yes, imaging confirmed cavernoma or vascular malformation', score: 4 },
      { value: 'suspicious-lesion', label: 'Yes, imaging showed suspicious brain lesion', score: 3 },
      { value: 'incidental-finding', label: 'Yes, incidental finding on brain scan', score: 2 },
      { value: 'no-imaging', label: 'No brain imaging or normal results', score: 0 }
    ]
  },
  {
    id: 'symptom-progression',
    question: 'How have your symptoms changed over time?',
    description: 'Symptom progression helps assess urgency and treatment needs',
    options: [
      { value: 'rapidly-worsening', label: 'Rapidly worsening symptoms over days to weeks', score: 4 },
      { value: 'gradually-worsening', label: 'Gradually worsening symptoms over months', score: 2 },
      { value: 'stable-symptoms', label: 'Stable symptoms without significant change', score: 1 },
      { value: 'no-progression', label: 'No symptoms or improving symptoms', score: 0 }
    ]
  }
];

export function CavernomaSymptomAssessment() {
  const deviceInfo = useDeviceDetection();
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [answers, setAnswers] = useState<Record<string, string>>({});
  const [showResults, setShowResults] = useState(false);
  const assessmentId = useId();

  const progress = ((currentQuestion + 1) / assessmentQuestions.length) * 100;
  const isLastQuestion = currentQuestion === assessmentQuestions.length - 1;
  const canProceed = answers[assessmentQuestions[currentQuestion]?.id];

  const handleAnswer = (value: string) => {
    setAnswers(prev => ({
      ...prev,
      [assessmentQuestions[currentQuestion].id]: value
    }));
  };

  const handleNext = () => {
    if (isLastQuestion) {
      setShowResults(true);
    } else {
      setCurrentQuestion(prev => prev + 1);
    }
  };

  const handlePrevious = () => {
    if (currentQuestion > 0) {
      setCurrentQuestion(prev => prev - 1);
    }
  };

  const calculateResults = (): AssessmentResult => {
    const totalScore = Object.entries(answers).reduce((total, [questionId, answer]) => {
      const question = assessmentQuestions.find(q => q.id === questionId);
      const option = question?.options.find(o => o.value === answer);
      return total + (option?.score || 0);
    }, 0);

    let likelihood: 'low' | 'moderate' | 'high';
    let recommendation: string;
    let urgency: 'routine' | 'urgent' | 'immediate';
    let nextSteps: string[];
    const symptoms: string[] = [];

    // Identify specific symptoms
    if (answers['seizure-history'] && !answers['seizure-history'].includes('no-seizures')) {
      symptoms.push('Seizures');
    }
    if (answers['bleeding-symptoms'] && !answers['bleeding-symptoms'].includes('no-sudden-symptoms')) {
      symptoms.push('Bleeding symptoms');
    }
    if (answers['neurological-deficits'] && !answers['neurological-deficits'].includes('no-deficits')) {
      symptoms.push('Neurological deficits');
    }
    if (answers['family-history'] && !answers['family-history'].includes('no-family-history')) {
      symptoms.push('Family history');
    }
    if (answers['imaging-findings'] && !answers['imaging-findings'].includes('no-imaging')) {
      symptoms.push('Imaging findings');
    }

    if (totalScore >= 15) {
      likelihood = 'high';
      urgency = 'urgent';
      recommendation = 'Your symptoms and history are highly concerning for a cavernoma that requires urgent medical evaluation. Multiple risk factors suggest the need for immediate assessment and likely brain imaging.';
      nextSteps = [
        'Contact your GP or neurologist immediately',
        'Request urgent brain MRI with susceptibility-weighted imaging',
        'Bring a list of all symptoms and their timeline',
        'Consider emergency department if symptoms are severe or worsening'
      ];
    } else if (totalScore >= 8) {
      likelihood = 'moderate';
      urgency = 'urgent';
      recommendation = 'Your symptoms suggest possible cavernoma involvement that should be evaluated promptly. Several concerning features warrant medical attention and likely brain imaging.';
      nextSteps = [
        'Schedule appointment with GP within 1-2 weeks',
        'Request referral to neurologist if symptoms persist',
        'Keep a symptom diary with dates and severity',
        'Discuss brain imaging (MRI) with your doctor'
      ];
    } else if (totalScore >= 3) {
      likelihood = 'low';
      urgency = 'routine';
      recommendation = 'You have some symptoms that could be related to various conditions. While cavernoma is less likely, medical evaluation is still recommended for proper assessment.';
      nextSteps = [
        'Discuss symptoms with your GP at next routine visit',
        'Monitor symptoms for any worsening',
        'Consider keeping a symptom diary',
        'Seek medical attention if symptoms worsen'
      ];
    } else {
      likelihood = 'low';
      urgency = 'routine';
      recommendation = 'Your symptoms are minimal and cavernoma is unlikely. Continue with routine health maintenance and monitor for any new symptoms.';
      nextSteps = [
        'Continue routine health check-ups',
        'Be aware of cavernoma warning signs',
        'Seek medical attention if new symptoms develop',
        'Maintain healthy lifestyle'
      ];
    }

    return { totalScore, likelihood, recommendation, urgency, nextSteps, symptoms };
  };

  const results = showResults ? calculateResults() : null;

  const getLikelihoodColor = (likelihood: string) => {
    switch (likelihood) {
      case 'high': return 'bg-muted-light text-foreground border border-border';
      case 'moderate': return 'bg-info-light text-info border border-info/50';
      default: return 'bg-success-light text-success border border-success/50';
    }
  };

  const getUrgencyIcon = (urgency: string) => {
    switch (urgency) {
      case 'immediate': return <AlertTriangle className="h-5 w-5 text-foreground" />;
      case 'urgent': return <Brain className="h-5 w-5 text-info" />;
      default: return <CheckCircle className="h-5 w-5 text-success" />;
    }
  };

  if (showResults && results) {
    return (
      <section className={cn("py-16 bg-muted", deviceInfo.isMobile ? "px-4" : "")}>
        <div className="container max-w-4xl">
          <Card className={cn("border-2", getLikelihoodColor(results.likelihood))}>
            <CardHeader className="text-center">
              <div className="flex items-center justify-center gap-2 mb-4">
                {getUrgencyIcon(results.urgency)}
                <CardTitle className="text-2xl">Cavernoma Assessment Results</CardTitle>
              </div>
              <CardDescription>
                Based on your responses, here's your personalised assessment
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Score and Likelihood */}
              <div className="text-center p-6 bg-background/50 rounded-lg">
                <div className="flex items-center justify-center gap-4 mb-4">
                  <div>
                    <div className="text-enhanced-heading text-3xl font-bold text-primary">{results.totalScore}</div>
                    <div className="text-sm text-muted-foreground">Assessment Score</div>
                  </div>
                  <div>
                    <Badge className={getLikelihoodColor(results.likelihood)}>
                      {results.likelihood.toUpperCase()} likelihood
                    </Badge>
                  </div>
                </div>
              </div>

              {/* Identified Symptoms */}
              {results.symptoms.length > 0 && (
                <div className="bg-background rounded-lg p-6 border">
                  <h3 className="font-semibold mb-3 flex items-centre gap-2">
                    <Zap className="h-5 w-5 text-info" />
                    Identified Risk Factors
                  </h3>
                  <div className="flex flex-wrap gap-2">
                    {results.symptoms.map((symptom, index) => (
                      <Badge key={index} variant="secondary" className="bg-info-light text-foreground">
                        {symptom}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {/* Recommendation */}
              <div className="bg-background rounded-lg p-6 border">
                <h3 className="font-semibold mb-3 flex items-centre gap-2">
                  <Activity className="h-5 w-5 text-primary" />
                  Recommendation
                </h3>
                <p className="text-sm">{results.recommendation}</p>
              </div>

              {/* Next Steps */}
              <div className="bg-background rounded-lg p-6 border">
                <h3 className="font-semibold mb-3 flex items-centre gap-2">
                  <ArrowRight className="h-5 w-5 text-primary" />
                  Recommended Next Steps
                </h3>
                <ul className="space-y-2">
                  {results.nextSteps.map((step, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <div className="w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-centre justify-centre text-xs font-bold mt-0.5 flex-shrink-0">
                        {index + 1}
                      </div>
                      <span className="text-sm">{step}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Important Disclaimer */}
              <div className="bg-info border border-info rounded-lg p-4">
                <div className="flex items-start gap-2">
                  <AlertTriangle className="h-5 w-5 text-info mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold text-info mb-1">Important Disclaimer</h4>
                    <p className="text-sm text-info">
                      This assessment tool is for educational purposes only and does not replace professional medical diagnosis. 
                      Many symptoms can have multiple causes. Always consult with qualified healthcare professionals for proper evaluation.
                    </p>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className={cn("flex gap-3", deviceInfo.isMobile ? "flex-col" : "flex-row justify-centre")}>
                <Button size="lg">
                  <Brain className="mr-2 h-4 w-4" />
                  Find a Specialist
                </Button>
                <Button variant="outline" size="lg" onClick={() => {
                  setShowResults(false);
                  setCurrentQuestion(0);
                  setAnswers({});
                }}>
                  Retake Assessment
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>
    );
  }

  return (
    <section 
      className={cn("py-16 bg-muted", deviceInfo.isMobile ? "px-4" : "")}
      aria-labelledby={`${assessmentId}-title`}
    >
      <div className="container max-w-3xl">
        <div className="text-center mb-8">
          <h2 
            id={`${assessmentId}-title`}
            className={cn("font-bold mb-4", deviceInfo.isMobile ? "text-2xl" : "text-3xl")}
          >
            Cavernoma Symptom Assessment
          </h2>
          <p className={cn("text-muted-foreground", deviceInfo.isMobile ? "text-sm" : "text-lg")}>
            Answer these questions to assess your symptoms and receive personalised guidance about when to seek medical attention
          </p>
        </div>

        {/* Progress */}
        <div className="mb-8">
          <div className="flex justify-between items-centre mb-2">
            <span className="text-sm font-medium">Progress</span>
            <span className="text-sm text-muted-foreground">
              {currentQuestion + 1} of {assessmentQuestions.length}
            </span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>

        {/* Question Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-centre gap-2">
              <TrendingUp className="h-5 w-5 text-primary" />
              Question {currentQuestion + 1}
            </CardTitle>
            <CardDescription className="text-lg font-medium">
              {assessmentQuestions[currentQuestion]?.question}
            </CardDescription>
            {assessmentQuestions[currentQuestion]?.description && (
              <p className="text-sm text-muted-foreground">
                {assessmentQuestions[currentQuestion].description}
              </p>
            )}
          </CardHeader>
          <CardContent>
            <RadioGroup
              value={answers[assessmentQuestions[currentQuestion]?.id] || ''}
              onValueChange={handleAnswer}
              className="space-y-3"
            >
              {assessmentQuestions[currentQuestion]?.options.map((option) => (
                <div key={option.value} className="flex items-start space-x-3 p-3 rounded-lg border hover:bg-muted/50">
                  <RadioGroupItem value={option.value} id={option.value} className="mt-1" />
                  <Label htmlFor={option.value} className="font-medium cursor-pointer flex-1">
                    {option.label}
                  </Label>
                </div>
              ))}
            </RadioGroup>
          </CardContent>
        </Card>

        {/* Navigation */}
        <div className={cn("flex justify-between mt-8", deviceInfo.isMobile ? "flex-col gap-3" : "")}>
          <Button
            variant="outline"
            onClick={handlePrevious}
            disabled={currentQuestion === 0}
            className={deviceInfo.isMobile ? "order-2" : ""}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Previous
          </Button>
          <Button
            onClick={handleNext}
            disabled={!canProceed}
            className={deviceInfo.isMobile ? "order-1" : ""}
          >
            {isLastQuestion ? 'Get Results' : 'Next'}
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </div>
    </section>
  );
}

export default CavernomaSymptomAssessment;
